from django.urls import path
from . import views
from .views import *
from django.contrib.auth.views import LogoutView, PasswordChangeView

urlpatterns = [
    path('register/', views.RegisterNewUserView.as_view(), name='register'),
    path('login/', CustomLoginView.as_view(), name='login'),
    path('ticket-map/', views.display_ticket_map, name='ticket_map'),
    path('tos/', TosView.as_view(), name='tos'),
    path('privacy/', PrivacyPolicy.as_view(), name='privacy'),
    path('register/<uuid:token>/', views.register_with_token, name='register_with_token'),
    path('contact/', contact_us, name='contact_us'),
    path('contact/success/', success_page, name='success_page'),
    path('api/is_task_running/', views.is_task_running, name='is_task_running'),
    path('logout/', LogoutView.as_view(next_page='home'), name='logout'),
    path('', HomeView.as_view(), name='home'),
    path('contractor_list/', ContractorListView.as_view(), name='contractor_list'),
    path('debris_report/<int:debris_area_id>/', views.generate_debris_report, name='debris_report'),
    path('sub_contractor_list/', SubContractorListView.as_view(), name='sub_contractor_list'),
    path('debris_list/', DebrisListView.as_view(), name='debris_list'),
    path('trucks_list/', TruckListView.as_view(), name='truck_list'),
    # path('project_list/', ProjectListView.as_view(), name='project_list'),
    # path('edit_project/<slug>', ProjectUpdateView.as_view(), name='edit_project'),
    # path('project_list/add_project', AddProjectView.as_view(), name='add_project'),
    path('add_truck/', AddTruckView.as_view(), name='add_truck'),
    path('yardage_chart/', yardage_chart_view, name='yardage_chart'),
    path('manual_create_ticket/<int:pk>/', ManualTicketCreateView.as_view(), name='manual_create_ticket'),
    path('export_tickets_to_csv', export_tickets_to_csv, name='export_tickets_to_csv'),
    path('failed_ticket_images/', FailedTicketImageListView.as_view(), name='failed_ticket_images'),
    path('add_debris/', AddDebrisView.as_view(), name='add_debris'),
    path('ticket_images/', TicketImageListView.as_view(), name='ticket_images'),
    path('upload_images/', AddImageView.as_view(), name='upload_images'),
    path('delete_project/', delete_project_view, name='delete_project_view'),
    path('upload_hangar_images/', AddHangarImageView.as_view(), name='upload_hangar_images'),
    path('edit_ticket/<int:pk>', TicketUpdateView.as_view(), name='edit_ticket'),
    path('edit_hangar_ticket/<int:pk>', HangarTicketUpdateView.as_view(), name='edit_hangar_ticket'),
    path('create_ticket/', TicketCreateView.as_view(), name='create_ticket'),
    path('ticket_list/', TicketListView.as_view(), name='ticket_list'),
    path('hangar_ticket_list/', HangarTicketListView.as_view(), name='hangar_ticket_list'),
    path('dashboard_home/', DashBoardHomeView.as_view(template_name="ticket_app/base/dashboard_home.html"), name='dashboard_home'),
    path('contractor_list/add_contractor/', AddContractorView.as_view(), name='add_contractor'),
    path('edit_contractor/<int:pk>', ContractorUpdateView.as_view(), name='edit_contractor'),
    path('edit_sub_contractor/<int:pk>', SubUpdateView.as_view(), name='edit_sub_contractor'),
    path('sub_contractor_list/add_sub/', AddSubView.as_view(), name='add_sub'),
    path('edit_sub/<int:pk>', SubUpdateView.as_view(), name='edit_sub'),
    path('edit_truck/<int:pk>', TruckUpdateView.as_view(), name='edit_truck'),
    path('edit_debris/<int:pk>', DebrisUpdateView.as_view(), name='edit_debris'),
    path('sub_pdf_file/<int:pk>/', views.debris_report_view, name='sub_debris_report'),
    path('late_sub_pdf_file/<int:pk>/', views.late_debris_report_view, name='late_sub_debris_report'),
    path('generate_report/<int:report_id>/', views.generate_report_view, name='generate_report'),    path("check_task_status/<str:task_id>/", views.check_task_status, name="check_task_status"),
    path("check_task_status/<str:task_id>/", views.check_task_status, name="check_task_status"),
    path('sub_hanger_pdf_file/<int:pk>/', views.hanger_report_view, name='hanger_report'),
    path('ticket_list/tickets/', views.tickets, name='tickets'),
    path('hangar_ticket_list/tickets/', views.hangar_tickets, name='hangar_tickets'),
    path('contractor_list/contractors/', views.contractors, name='contractors'),
    path('trucks_list/trucks/', views.trucks, name='trucks'),
    path('debris_list/debris/', views.debris, name='debris'),
    path('delete_truck/<int:pk>', TruckDeleteView.as_view(), name='delete_truck'),
    # path('delete_project/<slug>', ProjectDeleteView.as_view(), name='delete_project'),
    path('delete_contractor/<int:pk>', ContractorDeleteView.as_view(), name='delete_contractor'),
    path('delete_sub/<int:pk>', SubDeleteView.as_view(), name='delete_sub'),
    path('delete_debris/<int:pk>', DebrisDeleteView.as_view(), name='delete_debris'),
    path('delete_ticket/<int:pk>', TicketDeleteView.as_view(), name='delete_ticket'),
    path('delete_hanger_ticket/<int:pk>', HangerTicketDeleteView.as_view(), name='delete_hanger_ticket'),
    path('delete_ticket_image/<int:pk>', TicketImageDeleteView.as_view(), name='delete_ticket_image'),
    path('delete_failed_ticket_image/<int:pk>', FailedTicketImageDeleteView.as_view(), name='delete_failed_ticket_image'),
    # path('project_list/projects/', views.projects, name='projects'),
    path('thanks/', views.thanks, name='thanks'),
    path('checkout_enterprise/', views.checkout_enterprise, name='checkout_enterprise'),
    path('stripe_webhook/', views.stripe_webhook_enterprise, name='stripe_webhook/'),
    path('purchase_subscription/', views.PurchaseSubscriptionView.as_view(), name='purchase_subscription'),
    path('user_profile/', views.user_profile, name='user_profile'),
    path('password_change/', PasswordChangeView.as_view(template_name='ticket_app/user_profile/password_change.html'), name='password_change'),
    path('truck_report/<int:pk>/', views.generate_truck_report, name='truck_report'),
    path('customer-portal/', views.update_payment, name='customer_portal'),
    path('test-404/', views.test_404),
    path('test-403/', views.test_403),
    path('test-400/', views.test_400),
    path('test-500/', views.test_500),
    path('delete-subcontractor-project/<int:pk>/',SubcontractorProjectDeleteView.as_view(),name='delete_subcontractor_project'),
    path('messages/', views.get_messages, name='get_messages'),
    path('sub-users/', views.list_sub_users, name='list_sub_users'),
    path('sub-users/create/', views.create_sub_user, name='create_sub_user'),
    path('sub-users/delete/<int:user_id>/', views.delete_sub_user, name='delete_sub_user'),
    path('impersonate/<int:user_id>/', views.impersonate_user, name='impersonate_user'),
    path('impersonate/stop/', views.stop_impersonation, name='stop_impersonation'),
    path('compare/', views.compare_excels, name='compare_excels'),
]
