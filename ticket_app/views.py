import csv
import hashlib
import io
import json
import logging
import os
import traceback
from decimal import Decimal
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import Table, TableStyle, Image, PageBreak, Paragraph
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet
from django.http import FileResponse
import io
import os
import openpyxl
import pandas as pd
from celery.result import Async<PERSON><PERSON>ult
from decouple import config
from django.contrib.admin.models import LogEntry
from django.contrib.auth import authenticate, login, get_user_model, update_session_auth_hash
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import PasswordChangeForm, logger
from django.contrib.auth.models import Group
from django.core.exceptions import ImproperlyConfigured
from django.core.mail import send_mail
from django.core.paginator import Paginator
from django.db import IntegrityError, transaction
from django.db.models import <PERSON><PERSON>, <PERSON>, Subquery, OuterRef, DecimalField
from django.db.models.functions import Coalesce
from django.http import FileResponse, JsonResponse, HttpResponse, Http404, HttpResponseServerError, \
    HttpResponseBadRequest, HttpResponseForbidden, HttpResponseNotFound, HttpResponseRedirect
from django.shortcuts import render, redirect, get_object_or_404, resolve_url
from django.template.loader import render_to_string
from django.template.response import TemplateResponse
from django.utils import timezone
from django.utils.dateparse import parse_date
from django.utils.safestring import mark_safe
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic.list import ListView
from django.views.generic import TemplateView, DeleteView, UpdateView
from django.urls import reverse_lazy, reverse
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin, UserPassesTestMixin
from django_celery_results.models import TaskResult
from openpyxl.styles import Alignment, Font
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from bootstrap_modal_forms.generic import BSModalCreateView, BSModalUpdateView, BSModalDeleteView
from reportlab.platypus import Table, TableStyle, SimpleDocTemplate
from ticket_base import settings
from .filters import TruckFilter, ImageFilter
from .forms import *
from bootstrap_modal_forms.generic import BSModalLoginView
from .forms import CustomAuthenticationForm
from .models import TicketImage, SubContractor, HangarTicket, HangarTicketImage, RegistrationToken, UserTaskLock, \
    HistoricalTruckNumber
from .tasks import extract_img_text, extract_hanger_img_text, generate_debris_report_task
from django.db.models import Sum, Q
from django.db.models import Sum
from .models import Ticket


####################################
# HomeView and Login/RegisterView ##
####################################

def check_task_status(request, task_id):
    try:
        # Fetch the task result
        task_result = TaskResult.objects.get(task_id=task_id)
        result_data = json.loads(task_result.result)  # Ensure result is parsed

        logger.debug(f"Task {task_id} status: {task_result.status}, result: {result_data}")

        return JsonResponse({
            "status": task_result.status,
            "result": result_data
        })
    except TaskResult.DoesNotExist:
        logger.debug(f"Task {task_id} not found, status pending.")
        return JsonResponse({"status": "PENDING"}, status=200)
    except Exception as e:
        logger.error(f"Error fetching task {task_id} status: {str(e)}")
        return JsonResponse({"error": str(e)}, status=500)


@login_required
def export_tickets_to_csv(request):
    # Get the start and end dates from the request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date and end_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
        tickets = Ticket.objects.filter(author=request.user, Ticket_date__range=(start_date, end_date))
    else:
        tickets = Ticket.objects.filter(author=request.user)

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="tickets.csv"'

    writer = csv.writer(response)
    writer.writerow(['Ticket No', 'Truck No', 'Debris Type', 'Date', 'Load Call', 'Notes'])

    for ticket in tickets:
        writer.writerow(
            [ticket.ticket_no, ticket.Truck_No, ticket.debris_type, ticket.Ticket_date, ticket.load_call, ticket.Notes])

    return response


def get_daily_yardage():
    yardage_data = Ticket.objects.values('Truck_No__truck_number', 'Ticket_date').annotate(
        total_yardage=Sum('total_yardage')).order_by('Ticket_date')
    return yardage_data


class HomeView(TemplateView):
    template_name = 'ticket_app/base/home.html'


def display_ticket_map(request):
    map_file_path = "/tmp/ticket_pickup_locations_map.html"

    if os.path.exists(map_file_path):
        with open(map_file_path, 'r') as file:
            map_html = file.read()
        return HttpResponse(map_html)
    else:
        return HttpResponse("Map not available. Please generate it first.", status=404)


@login_required
def yardage_chart_view(request):
    user = request.user

    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        start_date = parse_date(start_date)
    if end_date:
        end_date = parse_date(end_date)

    date_filter = Q(Ticket_date__range=(start_date, end_date)) if start_date and end_date else Q()

    tickets = Ticket.objects.filter(author=user).filter(date_filter).select_related('Truck_No',
                                                                                    'Truck_No__paired_truck')

    truck_yardages = defaultdict(lambda: {"total_yardage": 0, "total_tickets": 0, "pair_key": ""})
    seen_pairs = set()

    for ticket in tickets:
        truck = ticket.Truck_No
        if truck.paired_truck:
            pair_key_tuple = tuple(sorted([truck.truck_number, truck.paired_truck.truck_number]))
            pair_key = f"{pair_key_tuple[0]} & {pair_key_tuple[1]}"

            if pair_key_tuple not in seen_pairs:
                seen_pairs.add(pair_key_tuple)
                driver_names = (
                    f" (Drivers: {truck.driver_name} & {truck.paired_truck.driver_name})"
                    if truck.driver_name or truck.paired_truck.driver_name else ""
                )
                truck_yardages[pair_key] = {
                    "pair_key": pair_key + driver_names,
                    "total_yardage": 0,
                    "total_tickets": 0,
                }
            truck_yardages[pair_key]["total_yardage"] += ticket.total_yardage or 0
            truck_yardages[pair_key]["total_tickets"] += 1
        else:
            # Use truck number as the key for individual trucks
            truck_key = f"{truck.truck_number} (Driver: {truck.driver_name})" if truck.driver_name else f"{truck.truck_number}"
            truck_yardages[truck_key]["pair_key"] = truck_key
            truck_yardages[truck_key]["total_yardage"] += ticket.total_yardage or 0
            truck_yardages[truck_key]["total_tickets"] += 1

    # Convert the defaultdict to a list of dicts for the template
    truck_yardages_list = list(truck_yardages.values())

    # Calculate total yardage and tickets for all trucks
    total_yardage = sum(data["total_yardage"] for data in truck_yardages_list)
    total_tickets = sum(data["total_tickets"] for data in truck_yardages_list)

    return render(request, 'yardage_chart.html', {
        'trucks': truck_yardages_list,
        'start_date': start_date,
        'end_date': end_date,
        'total_yardage': total_yardage,
        'total_tickets': total_tickets,
    })


class CustomLoginView(BSModalLoginView):
    authentication_form = CustomAuthenticationForm
    template_name = 'auth/login.html'
    success_message = 'Success: You were successfully logged in.'
    success_url = '/'


class RegisterNewUserView(BSModalCreateView):
    model = TicketUser
    template_name = 'auth/user_form.html'
    form_class = CreateUserForm
    success_url = reverse_lazy('home')

    def form_valid(self, form):
        # Extract user data from form
        email = form.cleaned_data.get('email')
        password = form.cleaned_data.get('password1')
        phone_number = form.cleaned_data.get('phone_number')
        billing_address = form.cleaned_data.get('billing_address')
        billing_city = form.cleaned_data.get('billing_city')
        billing_postal_code = form.cleaned_data.get('billing_postal_code')
        company_name = form.cleaned_data.get('company_name')

        user = form.save(commit=False)
        user.email = email
        user.set_password(password)
        user.phone_number = phone_number
        user.billing_address = billing_address
        user.billing_city = billing_city
        user.billing_postal_code = billing_postal_code
        user.company_name = company_name
        user.save()

        authenticated_user = authenticate(self.request, email=email, password=password)
        if authenticated_user is not None:
            login(self.request, authenticated_user)

        return super().form_valid(form)


def success_page(request):
    return render(request, 'success.html', {})


class PurchaseSubscriptionView(TemplateView):
    template_name = 'purchase_sub.html'


class TosView(TemplateView):
    template_name = 'ticket_app/tos.html'


class PrivacyPolicy(TemplateView):
    template_name = 'ticket_app/privacy.html'


def contact_us(request):
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            subject = form.cleaned_data['subject']
            message = f"Message from {form.cleaned_data['name']} ({form.cleaned_data['email']}): \n\n {form.cleaned_data['message']}"

            from_email = form.cleaned_data['email']

            # Send the email
            send_mail(
                subject,
                message,
                from_email,  # Must be a string
                ['<EMAIL>'],  # Recipient list
                fail_silently=False
            )

            return redirect('home')
    else:
        form = ContactForm()
    return render(request, 'ticket_app/contact.html', {'form': form})


##################
### ADD VIEWS ####
##################

# class AddProjectView(BSModalCreateView):
#     form_class = ProjectForm
#     template_name = 'ticket_app/projects/_project_add.html'
#     success_message = 'Success: Project was created'
#     success_url = reverse_lazy('project_list')
#
#     def form_valid(self, form):
#         form.instance.user = self.request.user
#         return super().form_valid(form)


def delete_project_view(request):
    if request.method == 'POST':
        project_id = request.POST.get('project_id')
        project = get_object_or_404(SubcontractorProject, pk=project_id)
        project.delete()
        return JsonResponse({'success': True})
    return JsonResponse({'error': 'Invalid request'}, status=400)


class AddContractorView(BSModalCreateView):
    form_class = ClientAddForm
    template_name = 'ticket_app/contractors/_contractor_add.html'
    success_message = 'Success: Contractor was created'
    success_url = reverse_lazy('contractor_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        form.user = self.request.user
        return form

    def form_valid(self, form):
        form.instance.user = self.request.user
        if form.cleaned_data.get("pay_rate") is None:
            form.instance.pay_rate = 0.0
        return super().form_valid(form)


SubcontractorProjectFormSet = inlineformset_factory(
    SubContractor,
    SubcontractorProject,
    form=SubcontractorProjectForm,
    extra=1,  # Change this to 0 if you don’t want empty extra forms
    can_delete=True
)


class AddSubView(BSModalCreateView):
    form_class = SubAddForm
    template_name = 'ticket_app/subcontractors/sub_contractor_add.html'
    success_message = 'Success: Sub was created'
    success_url = reverse_lazy('sub_contractor_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        if self.request.method == "POST":
            context['formset'] = SubcontractorProjectFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = SubcontractorProjectFormSet(instance=None, form_kwargs={'user': user})  # Empty formset

        return context

    def form_valid(self, form):
        if form.is_valid():
            sub_contractor_name = form.cleaned_data['sub_contractor_name'].strip()
            primary_contractor = form.cleaned_data['primary_contractor']
            user = self.request.user

            logger.info(f"Checking if subcontractor {sub_contractor_name} already exists.")

            exists = SubContractor.objects.filter(
                sub_contractor_name__iexact=sub_contractor_name,
                primary_contractor=primary_contractor,
                user=user
            ).exists()

            if exists:
                logger.warning(f"Subcontractor {sub_contractor_name} already exists! Preventing duplicate save.")
                form.add_error('sub_contractor_name', 'This subcontractor already exists.')
                return self.form_invalid(form)

            # Save subcontractor
            logger.info(f"Saving subcontractor {sub_contractor_name}.")
            subcontractor = form.save(commit=False)
            subcontractor.user = user
            subcontractor.sub_contractor_name = sub_contractor_name
            subcontractor.save()
            logger.info(f"Subcontractor {sub_contractor_name} saved successfully.")

            # Process formset
            formset = SubcontractorProjectFormSet(self.request.POST, instance=subcontractor)
            if formset.is_valid():
                formset.save()
                logger.info(f"SubcontractorProjectFormSet saved successfully.")
            else:
                logger.warning("SubcontractorProjectFormSet is invalid.")
                messages.error(self.request, "There was an error with the project formset.")
                return self.form_invalid(form)

            # Handle AJAX submission to close modal
            response_data = {
                "success": True,
                "redirect_url": str(self.success_url),
                "close_modal": True
            }
            logger.info(f"Returning JSON Response: {response_data}")

            if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse(response_data)

            messages.success(self.request, self.success_message)
            return HttpResponseRedirect(self.get_success_url())

        return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        return super().form_invalid(form)


class AddDebrisView(BSModalCreateView):
    form_class = DebrisAreaForm
    template_name = 'ticket_app/debris/_debris_add.html'
    success_message = 'Success: Debris area was created'
    success_url = reverse_lazy('debris_list')

    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)


class AddImageView(BSModalCreateView):
    model = TicketImage
    form_class = UploadFileForm
    template_name = 'ticket_app/tickets/_upload_image.html'
    success_url = reverse_lazy('ticket_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user  # Pass the current user to the form
        return kwargs

    def form_valid(self, form):
        try:
            newly_created_ticket_image_ids = []
            duplicate_images = []

            # Ensure debris_area belongs to the current user
            debris_area = form.cleaned_data.get('debris_area')
            if debris_area and debris_area.user != self.request.user:
                messages.error(self.request, "You cannot select a debris area that does not belong to you.")
                return self.form_invalid(form)

            # Process the remaining logic as before
            truck = form.cleaned_data.get('truck')
            image_date = form.cleaned_data.get('image_date')
            hauling_behind = form.cleaned_data.get('hauling_behind')
            late_ticket = form.cleaned_data.get('late_ticket')

            # Iterate over uploaded files
            for f in self.request.FILES.getlist('images'):
                try:
                    image_data = f.read()
                    image_hash = hashlib.sha256(image_data).hexdigest()
                    f.seek(0)

                    # Check for duplicates
                    if TicketImage.objects.filter(
                            image_hash=image_hash,
                            extraction_failed=False,
                            author=self.request.user
                    ).exists():
                        duplicate_images.append(f.name)
                        continue

                    ticket_image = TicketImage(
                        author=self.request.user,
                        debris_area=debris_area,
                        truck=truck,
                        image_date=image_date,
                        image_hash=image_hash
                    )
                    ticket_image.save()
                    ticket_image.images = f
                    ticket_image.save()
                    newly_created_ticket_image_ids.append(ticket_image.id)

                except Exception as e:
                    logging.error(f"Error processing image {f.name}: {str(e)}")
                    messages.error(self.request, f"Error processing image {f.name}")

            if newly_created_ticket_image_ids:
                # Process background task
                hauling_behind_id = hauling_behind.id if hauling_behind else None
                task = extract_img_text.delay(
                    newly_created_ticket_image_ids,
                    self.request.user.id,
                    hauling_behind_id,
                    late_ticket
                )
                self.request.session['task_id'] = task.id

            if duplicate_images:
                messages.warning(
                    self.request,
                    f"The following images were skipped due to duplication: {', '.join(duplicate_images)}"
                )

            return HttpResponseRedirect(self.success_url)

        except Exception as e:
            logging.error("Error occurred while creating ticket image: %s", traceback.format_exc())
            form.add_error(None, "Error occurred while creating ticket image. Please try again.")
            return self.form_invalid(form)


class ManualTicketCreateView(LoginRequiredMixin, UpdateView):
    model = TicketImage
    form_class = TicketForm
    template_name = 'ticket_app/tickets/manual_create_ticket.html'
    success_url = reverse_lazy('failed_ticket_images')

    def get_object(self):
        pk = self.kwargs.get('pk')
        logging.info(f"Attempting to retrieve TicketImage with pk: {pk}")
        return get_object_or_404(TicketImage, pk=pk, extracted=False)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user  # Pass the current user to the form
        return kwargs

    def post(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        try:
            logging.info(f"Creating ticket from ticket image ID: {pk}")
            ticket_image = TicketImage.objects.get(id=pk)

            form = TicketForm(request.POST, user=request.user)
            if not form.is_valid():
                logging.error(f"Form validation failed: {form.errors}")
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"Error in {field}: {error}")
                return redirect('failed_ticket_images')

            truck_instance = form.cleaned_data.get('Truck_No')
            if truck_instance is None:
                logging.error("Truck information is missing in the form.")
                messages.error(request, "Truck information is required.")
                return redirect('failed_ticket_images')
            contractor = truck_instance.sub_contractor if truck_instance else None

            debris_area_instance = form.cleaned_data.get('debris_area')
            if debris_area_instance is None:
                logging.error("Debris area is missing in the form.")
                messages.error(request, "Debris area is required.")
                return redirect('failed_ticket_images')

            ticket = Ticket(
                ticket_no=form.cleaned_data.get('ticket_no'),
                Truck_No=truck_instance,
                Contractor=contractor,
                debris_type=form.cleaned_data.get('debris_type'),
                debris_area=form.cleaned_data.get('debris_area'),
                hauling_behind=form.cleaned_data.get('hauling_behind'),
                Ticket_date=form.cleaned_data.get('Ticket_date'),
                load_call=form.cleaned_data.get('load_call', 0),
                Notes=form.cleaned_data.get('Notes', "Manually created from ticket image"),
                author=request.user,
                image_id=ticket_image,
                is_late=form.cleaned_data.get('is_late', False)
            )
            ticket.save()

            ticket_image.extracted = True
            ticket_image.extraction_failed = False
            ticket_image.ticket_created = True
            ticket_image.save()

            messages.success(request, "Ticket successfully created.")
        except TicketImage.DoesNotExist:
            logging.error(f"TicketImage with ID {pk} does not exist.")
            messages.error(request, "The ticket image does not exist.")
        except Exception as e:
            logging.error(f"Error occurred while creating ticket: {traceback.format_exc()}")
            messages.error(request, "An error occurred while creating the ticket. Please try again.")
        return redirect('failed_ticket_images')


class FailedTicketImageListView(LoginRequiredMixin, ListView):
    model = TicketImage
    template_name = 'ticket_app/tickets/failed_ticket_image_list.html'
    context_object_name = 'failed_images'

    def get_queryset(self):
        return TicketImage.objects.filter(extracted=False, extraction_failed=True, author=self.request.user)

    def post(self, request, *args, **kwargs):
        ticket_image_ids = request.POST.getlist('selected_images')
        if ticket_image_ids:
            task = extract_img_text.delay(ticket_image_ids, request.user.id)
            messages.success(request, f"Retry started for {len(ticket_image_ids)} failed images.")
        return self.get(request, *args, **kwargs)


class AddHangarImageView(BSModalCreateView):
    model = HangarTicketImage
    form_class = UploadHangerFileForm
    template_name = 'ticket_app/hangar_tickets/hanger_upload_image.html'
    success_url = reverse_lazy('hangar_ticket_list')

    def form_valid(self, form):
        try:
            ticket = form.save(commit=False)
            logging.info("Ticket instance created, not yet saved to the database.")

            ticket.author = self.request.user
            logging.info(f"Author set to: {self.request.user}")

            ticket.image_id = self.object
            logging.info(f"Ticket associated with TicketImage ID: {self.object.pk}")

            truck_instance = form.cleaned_data.get('Truck_No')
            if truck_instance and truck_instance.sub_contractor:
                ticket.Contractor = truck_instance.sub_contractor
                logging.info(f"Contractor set based on Truck_No: {truck_instance.sub_contractor}")

            ticket.save()
            logging.info(f"Ticket successfully saved with ticket_no: {ticket.ticket_no}")

            self.object.extracted = True
            self.object.extraction_failed = False
            self.object.ticket_created = True

            with self.object.images.open('rb') as f:
                image_data = f.read()
                self.object.image_hash = hashlib.sha256(image_data).hexdigest()

            self.object.save()
            logging.info(
                f"TicketImage updated: extracted={self.object.extracted}, ticket_created={self.object.ticket_created}")

            messages.success(self.request, "Ticket created successfully!")
            return super().form_valid(form)

        except Exception as e:
            # Log the exception for debugging
            logging.error(f"Error occurred during form validation: {e}")
            messages.error(self.request, "An error occurred while creating the ticket. Please try again.")
            return self.form_invalid(form)


class AddTruckView(BSModalCreateView):
    form_class = TruckForm
    template_name = 'ticket_app/trucks/_add_truck.html'
    success_message = 'Success: Truck was created'
    success_url = reverse_lazy('truck_list')

    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user  # add user parameter here
        return kwargs


class TicketCreateView(TemplateView):
    form = TicketForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = TicketForm(user=self.request.user)
        return context

    def get(self, request, *args, **kwargs):
        form = self.form()
        return render(request, 'ticket_app/tickets/_add_ticket.html', {'form': form})

    def post(self, request):
        form = TicketForm(request.POST or None)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = self.request.user
            ticket.save()
            return redirect(reverse('tickets'))
        else:
            return render(request, 'ticket_app/tickets/_add_ticket.html', {'form': form})


class HangerTicketCreateView(TemplateView):
    form = HangerTicketForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = HangerTicketForm(user=self.request.user)
        return context

    def get(self, request, *args, **kwargs):
        form = self.form()
        return render(request, 'ticket_app/hangar_tickets/_add_ticket.html', {'form': form})

    def post(self, request):
        form = HangerTicketForm(request.POST or None)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = self.request.user
            ticket.save()
            return redirect(reverse('hangar_tickets'))
        else:
            return render(request, 'ticket_app/hangar_tickets/_add_ticket.html', {'form': form})


#############################
######## LIST VIEWS #########
#############################
class DashBoardHomeView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    template_name = 'ticket_app/base/dashboard_home.html'
    form = ClientAddForm
    paginate_by = 10
    model = Ticket
    filter_class = TruckFilter

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        truck_filter = TruckFilter(self.request.GET, queryset=self.get_queryset())
        context['projected_income'] = Ticket.total_yards_together(self.request.user) * 5.00
        context['tickets'] = truck_filter.qs
        context['truck_filter'] = truck_filter
        context['tickets'] = truck_filter.qs
        context['ticket_count'] = context['truck_filter'].qs.count()
        selected_truck_tickets = Ticket.objects.filter(Truck_No=truck_filter.data.get('Truck_No'))
        context['selected_truck_total_yards'] = selected_truck_tickets.aggregate(Sum('total_yardage'))
        context['total_yardage_all_truck'] = Ticket.total_yards_together(self.request.user)
        context['ticket_count_extracted'] = TicketImage.objects.filter(author=self.request.user,
                                                                       extracted=True).count()
        context['ticket_count_not_extracted'] = TicketImage.objects.filter(author=self.request.user,
                                                                           extracted=False).count()
        recent_actions = context['recent_actions'] = LogEntry.objects.filter(user_id=self.request.user.id).order_by(
            '-action_time')[:5]

        for action in recent_actions:
            duration = datetime.now(action.action_time.tzinfo) - action.action_time
            duration_in_s = duration.total_seconds()
            if duration_in_s < 3600:  # one hour
                action.date = action.action_time.strftime("%M minutes ago")
            elif duration_in_s < 86400:  # one day
                action.date = action.action_time.strftime("%H hours ago")
            elif duration_in_s < 604800:  # one week
                action.date = action.action_time.strftime("%A, %H:%M")
            else:
                action.date = action.action_time.strftime("%Y-%m-%d, %H:%M")

        context['recent_actions'] = recent_actions
        return context

    def post(self, request):
        form = TicketForm(request.POST or None)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = self.request.user
            ticket.save()
            return redirect(reverse('tickets'))
        else:
            return render(request, 'ticket_app/tickets/_add_ticket.html', {'form': form})

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(author=self.request.user)
        return queryset


# class ProjectListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
#     paginate_by = 15
#     template_name = 'ticket_app/projects/project_list.html'
#     model = Project
#     form = ProjectForm
#
#     group_required = 'Enterprise'
#
#     def test_func(self):
#         return self.request.user.groups.filter(name=self.group_required).exists()
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['projects'] = Project.objects.filter(user=self.request.user)
#         context['form'] = ProjectForm
#
#         return context
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         queryset = queryset.filter(user=self.request.user)
#         return queryset


class DebrisListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 30
    template_name = 'ticket_app/debris/debris_list.html'
    model = DebrisArea
    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = DebrisAreaForm

        queryset = self.get_queryset()
        total_yardage = queryset.aggregate(total_yardage=Sum('ticket__total_yardage'))['total_yardage'] or 0
        context['total_yardage'] = total_yardage

        context['start_date'] = self.request.GET.get('start_date', '')
        context['end_date'] = self.request.GET.get('end_date', '')
        return context

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(user=self.request.user)

        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        # Build date filter
        date_filter = Q()
        if start_date and end_date:
            date_filter = Q(Ticket_date__range=[start_date, end_date])

        # Get direct tickets
        direct_tickets = Ticket.objects.filter(
            debris_area__id=OuterRef('id')
        ).filter(date_filter)

        # Get hauling behind tickets
        hauling_tickets = Ticket.objects.filter(
            hauling_behind__project__id=OuterRef('id'),
            hauling_behind__is_hauling_behind=True
        ).filter(date_filter)

        # Combine both types of tickets in the annotation
        return queryset.annotate(
            total_yardage=Coalesce(
                # Sum direct tickets
                Subquery(
                    direct_tickets.values('debris_area')
                    .annotate(total=Sum('total_yardage'))
                    .values('total')
                ),
                0,
                output_field=DecimalField(max_digits=10, decimal_places=2)
            ) +
                          Coalesce(
                              # Sum hauling behind tickets
                              Subquery(
                                  hauling_tickets.values('hauling_behind__project')
                                  .annotate(total=Sum('total_yardage'))
                                  .values('total')
                              ),
                              0,
                              output_field=DecimalField(max_digits=10, decimal_places=2)
                          )
        )


class ContractorListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 15
    template_name = 'ticket_app/contractors/contractor_list.html'
    model = Contractor
    form = ClientAddForm

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = ClientAddForm

        context['contractors'] = Contractor.objects.filter(user=self.request.user)

        return context

    def post(self, request):
        form = ClientAddForm(request.POST or None)
        if form.is_valid():
            contractor = form.save(commit=False)
            contractor.user = self.request.user
            contractor.save()
            return redirect(reverse('contractors'))
        else:
            return render(request, 'ticket_app/contractors/_contractor_add.html', {'form': form})

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(user=self.request.user)
        return queryset


class SubContractorListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 15
    template_name = 'ticket_app/subcontractors/sub_contractor_list.html'
    model = SubContractor
    form = ClientAddForm
    group_required = 'Enterprise'
    context_object_name = 'sub_contractors'  # Use this name in the template for the list

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = ClientAddForm

        # Use the paginated list of subcontractors from the context
        sub_contractors = context['sub_contractors']

        # Get debris areas for each subcontractor and store them in a dictionary
        subcontractor_debris_areas = {
            subcontractor.id: DebrisArea.objects.filter(ticket__Contractor=subcontractor).distinct()
            for subcontractor in sub_contractors
        }

        subcontractors_debris_type = {
            subcontractor.id: DebrisType.objects.filter(ticket__Contractor=subcontractor).distinct()
            for subcontractor in sub_contractors
        }

        # Get all distinct debris areas linked to tickets of subcontractors
        context['debris_areas'] = DebrisArea.objects.filter(
            ticket__Contractor__in=sub_contractors
        ).distinct()

        # Retrieve distinct debris types similarly
        context['debris_types'] = Ticket.objects.filter(
            Contractor__in=sub_contractors
        ).values_list('debris_type', flat=True).distinct()

        context['subcontractor_debris_areas'] = subcontractor_debris_areas
        context['subcontractors_debris_type'] = subcontractors_debris_type

        return context

    def post(self, request):
        form = ClientAddForm(request.POST or None)
        if form.is_valid():
            contractor = form.save(commit=False)
            contractor.user = self.request.user
            contractor.save()
            return redirect(reverse('sub_contractor_list'))
        else:
            return render(request, 'ticket_app/subcontractors/sub_contractor_add.html', {'form': form})

    def get_queryset(self):
        # Filter the queryset by the logged-in user
        return SubContractor.objects.filter(user=self.request.user)


class TicketImageListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = TicketImage
    template_name = 'ticket_app/tickets/ticket_image_list.html'
    # task = extract_img_text
    paginate_by = 10

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(author=self.request.user)
        queryset = ImageFilter(self.request.GET, queryset=queryset).qs
        return queryset

    def post(self, request, *args, **kwargs):
        selected_images = request.POST.getlist('selected_images[]')
        extract_img_text.delay(selected_images)
        messages.success(request, 'Your task has been started, check the progress in the task monitor.')
        return redirect('ticket_images')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ticket_count'] = TicketImage.objects.filter(author=self.request.user).count()
        context['image_filter'] = ImageFilter(self.request.GET, queryset=self.get_queryset())
        context['ticket_images'] = context['image_filter'].qs
        context['ticket_count_extracted'] = context['ticket_images'].filter(author=self.request.user,
                                                                            extracted=True).count()
        context['ticket_count_not_extracted'] = context['ticket_images'].filter(author=self.request.user,
                                                                                extracted=False).count()

        context['total_yardage_all_truck'] = Ticket.total_yards_together(self.request.user)
        recent_actions = context['recent_actions'] = LogEntry.objects.filter(user_id=self.request.user.id).order_by(
            '-action_time')[:5]
        for action in recent_actions:
            duration = datetime.now(action.action_time.tzinfo) - action.action_time
            duration_in_s = duration.total_seconds()
            if duration_in_s < 3600:  # one hour
                action.date = action.action_time.strftime("%M minutes ago")
            elif duration_in_s < 86400:  # one day
                action.date = action.action_time.strftime("%H hours ago")
            elif duration_in_s < 604800:  # one week
                action.date = action.action_time.strftime("%A, %H:%M")
            else:
                action.date = action.action_time.strftime("%Y-%m-%d, %H:%M")

        context['recent_actions'] = recent_actions
        return context


class TicketListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 15
    model = Ticket
    filterset_class = TruckFilter
    template_name = 'ticket_app/tickets/ticket_list.html'

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_filterset_kwargs(self, filterset_class):
        kwargs = super().get_filterset_kwargs(filterset_class)
        kwargs['user'] = self.request.user
        kwargs['filterset_class'] = TruckFilter
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        failed_ticket_count = TicketImage.objects.filter(author=self.request.user, extraction_failed=True,
                                                         extracted=False).count()
        context['failed_ticket_count'] = failed_ticket_count
        context['ticket_create_form'] = TicketForm(user=self.request.user)
        context['task_id'] = self.request.session.get('task_id')

        truck_filter_data = self.request.GET or self.request.session.get('truck_filter', {})

        context['truck_filter'] = TruckFilter(truck_filter_data, queryset=self.get_queryset(), user=self.request.user)
        context['tickets'] = context['truck_filter'].qs
        context['ticket_count'] = context['tickets'].count()

        self.request.session['truck_filter'] = truck_filter_data

        context['truck_filter'] = TruckFilter(truck_filter_data, queryset=self.get_queryset(), user=self.request.user)
        context['tickets'] = context['truck_filter'].qs
        context['ticket_count'] = context['tickets'].count()

        context['recently_edited_tickets'] = Ticket.objects.filter(author=self.request.user).select_related('Truck_No',
                                                                                                            'Contractor',
                                                                                                            'debris_type',
                                                                                                            'debris_area',
                                                                                                            'author',
                                                                                                            'image_id').order_by(
            '-last_updated')[:10]

        ticket_counts = Ticket.objects.filter(author=self.request.user).aggregate(
            questionable_tickets_count=Count('ticket_id', filter=Q(questionable=True)),
            good_tickets_count=Count('ticket_id', filter=Q(questionable=False))
        )
        context['questionable_tickets'] = ticket_counts['questionable_tickets_count']
        context['good_tickets'] = ticket_counts['good_tickets_count']

        ticket_image_counts = TicketImage.objects.filter(author=self.request.user).aggregate(
            ticket_count_extracted=Count('id', filter=Q(extracted=True)),
            ticket_count_not_extracted=Count('id', filter=Q(extracted=False))
        )
        context['ticket_count_extracted'] = ticket_image_counts['ticket_count_extracted']
        context['ticket_count_not_extracted'] = ticket_image_counts['ticket_count_not_extracted']

        context['total_yardage_all_truck'] = Ticket.total_yards_together(self.request.user)

        self.request.session['truck_filter'] = context['truck_filter'].data
        return context

    def get(self, request, *args, **kwargs):
        # If user asked to clear filters, delete the saved querystring
        if request.GET.get('clear_filters') == '1':
            request.session.pop('ticket_filter_querystring', None)

        # Otherwise persist current filters for delete-redirects
        else:
            request.session['ticket_filter_querystring'] = request.META.get('QUERY_STRING', '')

        return super().get(request, *args, **kwargs)

    def post(self, request):
        form = TicketForm(request.POST or None)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = self.request.user
            ticket.save()
            return redirect(reverse('tickets'))
        else:
            return render(request, 'ticket_app/tickets/_add_ticket.html', {'form': form})

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-last_updated')
        queryset = queryset.filter(author=self.request.user).select_related('Truck_No', 'Contractor', 'debris_type',
                                                                            'debris_area', 'author', 'image_id')
        queryset = TruckFilter(self.request.GET, queryset=queryset, user=self.request.user).qs
        return queryset

    def ticket_image(self, obj):
        return mark_safe('<img src="{url}" width="500" height="500" />'.format(
            url=obj.image_id.images.url
        ))


class HangarTicketListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 15
    model = HangarTicket
    filterset_class = TruckFilter
    template_name = 'ticket_app/hangar_tickets/hangar_ticket_list.html'

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_filterset_kwargs(self, filterset_class):
        kwargs = super().get_filterset_kwargs(filterset_class)
        kwargs['user'] = self.request.user
        kwargs['filterset_class'] = TruckFilter
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ticket_create_form'] = HangerTicketForm(user=self.request.user)
        context['task_id'] = self.request.session.get('task_id')
        context['truck_filter'] = TruckFilter(self.request.GET or self.request.session.get('truck_filter', {}),
                                              queryset=self.get_queryset(), user=self.request.user)
        context['tickets'] = context['truck_filter'].qs
        context['ticket_count'] = context['tickets'].count()
        context['questionable_tickets'] = HangarTicket.objects.filter(author=self.request.user,
                                                                      questionable=True).count()
        context['good_tickets'] = HangarTicket.objects.filter(author=self.request.user, questionable=False).count()
        context['ticket_count_extracted'] = TicketImage.objects.filter(author=self.request.user, extracted=True).count()
        context['ticket_count_not_extracted'] = TicketImage.objects.filter(author=self.request.user,
                                                                           extracted=False).count()
        self.request.session['truck_filter'] = context['truck_filter'].data

        return context

    def post(self, request):
        form = HangerTicketForm(request.POST or None)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = self.request.user
            ticket.save()
            return redirect(reverse('hangar_tickets'))
        else:
            return render(request, 'ticket_app/hangar_tickets/_add_ticket.html', {'form': form})

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(author=self.request.user)
        queryset = TruckFilter(self.request.GET, queryset=queryset, user=self.request.user).qs
        return queryset

    def ticket_image(self, obj):
        return mark_safe('<img src="{url}" width="500" height="500" />'.format(
            url=obj.image_id.images.url
        ))


class TruckListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    paginate_by = 20
    model = Truck
    template_name = 'ticket_app/trucks/trucks_list.html'

    group_required = 'Enterprise'

    def test_func(self):
        return self.request.user.groups.filter(name=self.group_required).exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = TruckFilterForm
        context['tickets'] = Ticket.objects.filter(author=self.request.user)

        trucks = self.get_queryset()
        truck_data = []

        # Create a list of truck dictionaries that include pairing info
        for truck in trucks:
            if truck.paired_truck:
                pair_info = f"Paired with {truck.paired_truck.truck_number} (Driver: {truck.paired_truck.driver_name})"
            else:
                pair_info = "Not Paired"
            truck_data.append({
                'truck': truck,
                'pair_info': pair_info,
            })

        context['trucks'] = truck_data

        context['ticket_count'] = TicketImage.objects.filter(author=self.request.user).count()
        context['ticket_count_extracted'] = TicketImage.objects.filter(author=self.request.user, extracted=True).count()
        context['ticket_count_not_extracted'] = TicketImage.objects.filter(author=self.request.user,
                                                                           extracted=False).count()
        context['total_yardage_all_truck'] = Ticket.total_yards_together(self.request.user)

        recent_actions = LogEntry.objects.filter(user_id=self.request.user.id).order_by('-action_time')[:5]
        for action in recent_actions:
            duration = datetime.now(action.action_time.tzinfo) - action.action_time
            duration_in_s = duration.total_seconds()
            if duration_in_s < 3600:  # one hour
                action.date = action.action_time.strftime("%M minutes ago")
            elif duration_in_s < 86400:  # one day
                action.date = action.action_time.strftime("%H hours ago")
            elif duration_in_s < 604800:  # one week
                action.date = action.action_time.strftime("%A, %H:%M")
            else:
                action.date = action.action_time.strftime("%Y-%m-%d, %H:%M")

        context['recent_actions'] = recent_actions
        return context

    def post(self, request):
        form = TruckForm(request.POST or None)
        if form.is_valid():
            truck = form.save(commit=False)
            truck.user = self.request.user
            truck.save()
            return redirect(reverse('truck_list'))

    def get_queryset(self):
        queryset = super().get_queryset().filter(user=self.request.user).select_related('paired_truck')

        # Initialize the filter form
        form = TruckFilterForm(self.request.GET or None)

        # Apply filters if form is valid
        if form.is_valid():
            truck_number = form.cleaned_data.get('truck_number')
            driver_name = form.cleaned_data.get('driver_name')
            sub_contractor = form.cleaned_data.get('sub_contractor')

            if truck_number:
                queryset = queryset.filter(truck_number__icontains=truck_number)
            if driver_name:
                queryset = queryset.filter(driver_name__icontains=driver_name)
            if sub_contractor:
                queryset = queryset.filter(sub_contractor=sub_contractor)

        return queryset


###########################
###### UPDATE VIEWS #######
###########################

class TicketUpdateView(BSModalUpdateView):
    model = Ticket
    form_class = TicketUpdateForm
    template_name = 'ticket_app/tickets/_ticket_update.html'
    success_message = 'Success: Ticket Updated.'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def get_success_url(self):
        # Preserve the filter settings in the URL after update
        return f"{reverse_lazy('ticket_list')}?{self.request.session.get('ticket_filter_querystring', '')}"


class HangarTicketUpdateView(BSModalUpdateView):
    model = HangarTicket
    form_class = HangerTicketUpdateForm
    template_name = 'ticket_app/hangar_tickets/_ticket_update.html'
    success_message = 'Success: Ticket Updated.'
    success_url = reverse_lazy('hangar_ticket_list')


class TruckUpdateView(BSModalUpdateView):
    model = Truck
    model = Truck
    form_class = TruckForm
    template_name = 'ticket_app/trucks/_truck_update.html'
    success_message = 'Success: Truck Updated.'
    success_url = reverse_lazy('truck_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs


# class ProjectUpdateView(BSModalUpdateView):
#     model = Project
#     form_class = ProjectForm
#     template_name = 'ticket_app/projects/_project_update.html'
#     success_message = 'Success: Project Updated.'
#     success_url = reverse_lazy('project_list')


class ContractorUpdateView(BSModalUpdateView):
    model = Contractor
    form_class = ClientAddForm
    template_name = 'ticket_app/contractors/_contractor_update.html'
    success_message = 'Success: Client Updated.'
    success_url = reverse_lazy('contractor_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs


class SubUpdateView(BSModalUpdateView):
    model = SubContractor
    form_class = SubUpdateForm
    template_name = 'ticket_app/subcontractors/sub_contractor_update.html'
    success_message = 'Success: Subcontractor updated successfully'
    success_url     = reverse_lazy('sub_contractor_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.method == 'POST':
            context['formset'] = SubcontractorProjectFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = SubcontractorProjectFormSet(instance=self.object)
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        if formset.is_valid():
            # Save the main form and the inline formset
            self.object = form.save()
            formset.instance = self.object
            formset.save()
            # Return the flag to close the modal
            return self.render_json_response({'close_modal': True})
        else:
            # Re-render the modal with errors
            return self.render_json_response(self.get_context_data())

class DebrisUpdateView(BSModalUpdateView):
    model = DebrisArea
    form_class = DebrisAreaForm
    template_name = 'ticket_app/debris/_debris_update.html'
    success_message = 'Success: Debris area Updated.'
    success_url = reverse_lazy('debris_list')


##################################
######### DELETE VIEWS ###########
##################################

from django.http import Http404
from django.urls import reverse_lazy
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic.edit import DeleteView


class ErrorCheckedDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    success_url = None
    success_message = None
    error_message = None

    def delete(self, request, *args, **kwargs):
        try:
            return super().delete(request, *args, **kwargs)
        except Http404:
            messages.error(request, self.error_message)
            return self.handle_no_permission()
        except IntegrityError:
            messages.error(request, "Cannot delete this record as it has related records in other tables.")
            return self.handle_no_permission()
        except Exception as e:
            messages.error(request, str(e))
            return self.handle_no_permission()

    def get_success_url(self):
        if self.success_url is None:
            raise ImproperlyConfigured("No URL to redirect to. Provide a success_url.")
        return str(self.success_url)

    def get_success_message(self):
        if self.success_message is None:
            raise ImproperlyConfigured("No success message. Provide a success_message.")
        return str(self.success_message)


# class ProjectDeleteView(ErrorCheckedDeleteView):
#     model = Project
#     permission_required = 'ticket_app.view_truck'
#     success_url = reverse_lazy('project_list')
#     success_message = "Project deleted!"
#     error_message = "Project cannot be deleted because it is referenced by other objects."
#     template_name = 'ticket_app/projects/_delete_project.html'


class SubcontractorProjectDeleteView(LoginRequiredMixin, DeleteView):
    model = SubcontractorProject
    template_name = 'ticket_app/subcontractors/_delete_subcontractor_project.html'
    success_url = reverse_lazy('sub_contractor_list')  # Redirect after deletion
    success_message = "Subcontractor project deleted successfully."

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        project_id = kwargs.get('pk')  # Retrieve the project ID from the URL
        try:
            self.object.delete()
            response = {'success': True, 'message': self.success_message}
        except Exception as e:
            response = {'success': False, 'error': str(e)}
        return JsonResponse(response)


class TruckDeleteView(ErrorCheckedDeleteView):
    model = Truck
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('truck_list')
    success_message = "Truck deleted!"
    error_message = "Truck cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/trucks/_delete_truck.html'


class ContractorDeleteView(ErrorCheckedDeleteView):
    model = Contractor
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('contractor_list')
    success_message = "Contractor deleted!"
    error_message = "Contractor cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/contractors/_delete_contractor.html'


class SubDeleteView(ErrorCheckedDeleteView):
    model = SubContractor
    permission_required = 'ticket_app.delete_subcontractor'
    success_url = reverse_lazy('sub_contractor_list')
    success_message = "Sub deleted!"
    error_message = "Sub cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/subcontractors/sub_delete_contractor.html'


class DebrisDeleteView(ErrorCheckedDeleteView):
    model = DebrisArea
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('debris_list')
    success_message = "Debris Area deleted!"
    error_message = "Debris Area cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/debris/_delete_debris.html'


class TicketDeleteView(ErrorCheckedDeleteView):
    model = Ticket
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('ticket_list')
    success_message = "Ticket deleted!"
    error_message = "Ticket cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/tickets/_delete_ticket.html'

    def get_success_url(self):
        querystring = self.request.session.get('ticket_filter_querystring', '')
        return f"{reverse_lazy('ticket_list')}?{querystring}" if querystring else reverse_lazy('ticket_list')


class HangerTicketDeleteView(ErrorCheckedDeleteView):
    model = HangarTicket
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('hangar_ticket_list')
    success_message = "Ticket deleted!"
    error_message = "Ticket cannot be deleted because it is referenced by other objects."
    template_name = 'ticket_app/hangar_tickets/_delete_hanger_ticket.html'


class TicketImageDeleteView(ErrorCheckedDeleteView):
    model = TicketImage
    permission_required = 'ticket_app.view_truck'
    success_message = "Ticket Image deleted!"
    error_message = "Ticket Image not found."
    template_name = 'ticket_app/hangar_tickets/_delete_ticket_image.html'


class FailedTicketImageDeleteView(ErrorCheckedDeleteView):
    model = TicketImage
    permission_required = 'ticket_app.view_truck'
    success_url = reverse_lazy('failed_ticket_images')
    success_message = "Failed ticket image deleted!"
    error_message = "Failed ticket image not found."
    template_name = 'ticket_app/tickets/_delete_failed_ticket_image.html'

    def get_queryset(self):
        # Only allow deletion of failed ticket images belonging to the current user
        return TicketImage.objects.filter(
            extracted=False,
            extraction_failed=True,
            author=self.request.user
        )

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(request, self.success_message)
        return response


#############################
#### PDF GENERATION #########
#############################

def get_historical_truck_number(truck, ticket_date):
    historical_record = HistoricalTruckNumber.objects.filter(
        truck=truck,
        start_date__lte=ticket_date,
        end_date__gte=ticket_date if ticket_date else timezone.now()
    ).first()
    return historical_record.truck_number if historical_record else truck.truck_number


from collections import defaultdict
from reportlab.lib import colors


def combine_invoices(reports, zip_filename):
    """
    Combines multiple reports into a single ZIP file.
    """
    from zipfile import ZipFile
    import io

    zip_buffer = io.BytesIO()
    with ZipFile(zip_buffer, 'w') as zip_file:
        for filename, content in reports:
            zip_file.writestr(filename, content)
    zip_buffer.seek(0)
    return zip_buffer


def debug_subcontractor_data(subcontractor_id, start_date, end_date):
    """
    Debug function to verify all relationships and data for a subcontractor.
    """
    print("\n=== Starting Debug Analysis ===\n")

    # 1. Check Subcontractor
    try:
        subcontractor = SubContractor.objects.get(id=subcontractor_id)
        print(f"Found Subcontractor: {subcontractor.sub_contractor_name} (ID: {subcontractor.id})")
    except SubContractor.DoesNotExist:
        print(f"No subcontractor found with ID: {subcontractor_id}")
        return

    # 2. Check Trucks
    trucks = Truck.objects.filter(sub_contractor=subcontractor)
    print(f"\nTrucks belonging to subcontractor:")
    for truck in trucks:
        print(f"- Truck {truck.truck_number} (ID: {truck.id})")

    # 3. Check Raw Tickets
    all_tickets = Ticket.objects.filter(
        Truck_No__in=trucks,
        Ticket_date__range=(start_date, end_date)
    )

    print(f"\nFound {all_tickets.count()} total tickets")
    print("\nTicket details:")
    for ticket in all_tickets:
        print(f"""
        Ticket: {ticket.ticket_no}
        Date: {ticket.Ticket_date}
        Truck: {ticket.Truck_No.truck_number}
        Debris Area: {ticket.debris_area.area if ticket.debris_area else 'None'}
        Debris Type: {ticket.debris_type.debris_type if ticket.debris_type else 'None'}
        Load Call: {ticket.load_call}%
        Total Yardage: {ticket.total_yardage}
        """)

    # 4. Check Debris Areas
    debris_areas = set(all_tickets.exclude(debris_area__isnull=True)
                       .values_list('debris_area__area', 'debris_area__id'))

    print("\nDebris Areas found in tickets:")
    for area_name, area_id in debris_areas:
        area_tickets = all_tickets.filter(debris_area_id=area_id)
        print(f"- {area_name} (ID: {area_id})")
        print(f"  Total tickets in this area: {area_tickets.count()}")
        print(f"  Total yardage in this area: {sum(t.total_yardage or 0 for t in area_tickets)}")

    # 5. Check Pay Rates
    print("\nChecking pay rates:")
    for area_name, area_id in debris_areas:
        try:
            pay_rate = SubcontractorProject.objects.get(
                subcontractor=subcontractor,
                project_id=area_id
            ).pay_rate
            print(f"- {area_name}: ${pay_rate}/yard")
        except SubcontractorProject.DoesNotExist:
            print(f"- {area_name}: No pay rate found")

    print("\n=== Debug Analysis Complete ===\n")


def generate_late_ticket_report(request, pk):
    """
    Generates separate reports ONLY for late tickets grouped by debris area.
    Combines them into a single ZIP file.
    """
    try:
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")

        if not all([start_date, end_date]):
            return HttpResponse("Start date and end date are required.", status=400)

        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        subcontractor = SubContractor.objects.get(id=pk)

        # Filter only late tickets
        tickets = Ticket.objects.filter(
            Truck_No__sub_contractor=subcontractor,
            Ticket_date__range=(start_date, end_date),
            is_late=True
        )

        print("\n[DEBUG] FILTERED tickets in generate_late_ticket_report():")
        for t in tickets:
            print(f"Ticket #{t.ticket_no} | Late: {t.is_late}")

        if not tickets.exists():
            messages.warning(request, "No late tickets found for the selected period.")
            return HttpResponse(status=204)

        report_buffers = []

        debris_area_ids = tickets.values_list('debris_area', flat=True).distinct()

        for debris_area_id in debris_area_ids:
            if debris_area_id:
                debris_area = DebrisArea.objects.get(id=debris_area_id)
                area_tickets = tickets.filter(debris_area=debris_area)

                if area_tickets.exists():
                    reports = generate_report_for_area(
                        subcontractor=subcontractor,
                        debris_area=debris_area,
                        tickets=area_tickets,
                        start_date=start_date,
                        end_date=end_date
                    )
                    report_buffers.append((
                        f"{subcontractor}_{debris_area.area}_LATE_debris_area_report.pdf",
                        reports["pdf"].getvalue()
                    ))
                    report_buffers.append((
                        f"{subcontractor}_{debris_area.area}_LATE_debris_area_report.xlsx",
                        reports["excel"].getvalue()
                    ))

        if not report_buffers:
            messages.warning(request, "No reports generated for late tickets in the selected period.")
            return HttpResponse(status=204)

        zip_buffer = combine_invoices(report_buffers,
                                      f"{subcontractor.sub_contractor_name}_late_tickets_{start_date}_to_{end_date}")

        return FileResponse(
            zip_buffer,
            as_attachment=True,
            filename=f"{subcontractor.sub_contractor_name}_late_tickets_{start_date}_to_{end_date}.zip"
        )

    except Exception as e:
        import traceback
        trace = traceback.format_exc()
        print(f"Error: {str(e)}\n{trace}")
        return HttpResponse(f"Error generating late ticket reports: {str(e)}", status=500)


def generate_subcontractor_report(request, pk):
    """
    Generates separate reports for tickets grouped by both debris area and hauling behind.
    Combines them into a single ZIP file.
    """
    try:
        # Parse dates
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")

        if not all([start_date, end_date]):
            return HttpResponse("Start date and end date are required.", status=400)

        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        # Get subcontractor
        subcontractor = SubContractor.objects.get(id=pk)

        # Fetch all tickets for the subcontractor within the date range
        tickets = Ticket.objects.filter(
            Truck_No__sub_contractor=subcontractor,
            Ticket_date__range=(start_date, end_date),
            is_late=False
        )

        if not tickets.exists():
            messages.warning(request, "No tickets found for the selected period.")
            return HttpResponse(status=204)  # 204 No Content

        # Group tickets by debris area and hauling behind
        debris_area_ids = tickets.values_list('debris_area', flat=True).distinct()
        hauling_project_ids = tickets.values_list('hauling_behind', flat=True).distinct()

        # Generate reports for each grouping
        report_buffers = []

        # Generate reports for debris areas
        for debris_area_id in debris_area_ids:
            if debris_area_id:
                debris_area = DebrisArea.objects.get(id=debris_area_id)
                area_tickets = tickets.filter(debris_area=debris_area)

                if area_tickets.exists():
                    reports = generate_report_for_area(
                        subcontractor=subcontractor,
                        debris_area=debris_area,
                        tickets=area_tickets,
                        start_date=start_date,
                        end_date=end_date
                    )
                    # Add both PDF and Excel to the list
                    report_buffers.append(
                        (f"{subcontractor}_{debris_area.area}_debris_area_report.pdf", reports["pdf"].getvalue()))
                    report_buffers.append(
                        (f"{subcontractor}_{debris_area.area}_debris_area_report.xlsx", reports["excel"].getvalue()))

        if not report_buffers:
            messages.warning(request, "No reports generated - no valid data for report period.")
            return HttpResponse(status=204)  # 204 No Content

        # Combine reports into a single ZIP file
        zip_buffer = combine_invoices(report_buffers,
                                      f"{subcontractor.sub_contractor_name}_reports_{start_date}_to_{end_date}")

        return FileResponse(
            zip_buffer,
            as_attachment=True,
            filename=f"{subcontractor.sub_contractor_name}_reports_{start_date}_to_{end_date}.zip"
        )

    except Exception as e:
        import traceback
        trace = traceback.format_exc()
        print(f"Error: {str(e)}\n{trace}")
        return HttpResponse(f"Error generating reports: {str(e)}", status=500)


def generate_report_for_area(subcontractor, debris_area, tickets, start_date, end_date, debris_type=None):
    """
    Generates a detailed report for a specific debris area as both a PDF and an Excel file.
    """
    # ---- Initialize buffers ----
    pdf_buffer = io.BytesIO()
    excel_buffer = io.BytesIO()

    # ---- PDF Generation ----
    canv = canvas.Canvas(pdf_buffer, pagesize=letter, bottomup=1)

    # Get pay rate
    pay_rate = SubcontractorProject.objects.filter(
        subcontractor=subcontractor,
        project=debris_area
    ).values_list('pay_rate', flat=True).first() or Decimal('0.00')

    # Fetch tickets
    tickets = tickets.order_by('Ticket_date')  # <-- use the argument passed in

    if debris_type:
        tickets = tickets.filter(debris_type__in=debris_type)

    # Prepare table data
    table_headers = ['Date', 'Ticket#', 'Truck#', 'Debris Type', 'Load %', 'Yards', 'Pay Rate', 'Amount']
    table_data = [table_headers]

    total_payment = Decimal('0.00')
    total_yardage = Decimal('0.00')

    ticket_occurrences = defaultdict(int)

    # Populate data for both PDF & Excel
    for ticket in tickets:
        yardage = ticket.total_yardage or Decimal('0.00')
        amount = yardage * pay_rate
        total_payment += amount
        total_yardage += yardage

        row = [
            ticket.Ticket_date.strftime('%Y-%m-%d'),
            ticket.ticket_no or "Unknown",
            ticket.historical_truck_number or "Unknown",
            ticket.debris_type.debris_type if ticket.debris_type else "Unknown",
            f"{ticket.load_call}" if ticket.load_call else "0",
            f"{yardage:.2f}",
            f"${pay_rate:.2f}",
            f"${amount:.2f}"
        ]
        table_data.append(row)
        ticket_occurrences[row[1]] += 1  # Track duplicates

    # Retainage calculation
    retainage = total_payment * Decimal('0.10')
    net_pay = total_payment - retainage

    # ---- Generate PDF ----
    rows_on_page = 30
    total_pages = (len(table_data) - 1) // rows_on_page + 1
    page_count = 1
    i = 0

    while i < len(table_data):
        canv.drawImage('static/img/medium.png', 25, 725, 100, 100, mask='auto', preserveAspectRatio=True)

        canv.setFont("Helvetica", 10)
        canv.drawString(250, 775, "Debris Report")
        canv.drawString(450, 775, f"Report Created: {datetime.now().date()}")
        canv.drawString(10, 740, f"Subcontractor: {subcontractor.sub_contractor_name}")
        canv.drawString(455, 765, f"Period: {start_date} to {end_date}")

        canv.drawString(10, 730, f"Total Yards: {total_yardage:.2f}")
        canv.drawString(10, 720, f"Debris Area: {debris_area.area}")
        canv.drawString(450, 740, f"Pay Rate: ${pay_rate:.2f}")
        canv.drawString(450, 730, f"Gross Pay: ${total_payment:.2f}")
        canv.drawString(450, 720, f"Retainage: -${retainage:.2f}")
        canv.drawString(450, 710, f"Net Pay: ${net_pay:.2f}")

        canv.line(0, 750, 650, 750)
        canv.line(0, 700, 650, 705)

        page_rows = table_data[i + 1:i + rows_on_page + 1]
        table_data_for_page = [table_headers] + page_rows

        table = Table(table_data_for_page, colWidths=[80, 120, 50, 70, 70, 40, 40, 40])

        table_styles = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]

        for row_idx, row in enumerate(page_rows, start=1):  # start=1 because 0 is header
            ticket_number = row[1]
            if ticket_occurrences[ticket_number] > 1:
                table_styles.append(('BACKGROUND', (0, row_idx), (-1, row_idx), colors.yellow))

        table.setStyle(TableStyle(table_styles))
        table.wrapOn(canv, 200, 200)
        table.drawOn(canv, 10, 130)

        canv.setFont("Helvetica", 6)
        canv.drawString(570, 10, f"Page {page_count} of {total_pages}")

        canv.showPage()
        page_count += 1
        i += rows_on_page

    canv.save()
    pdf_buffer.seek(0)

    # ---- Generate Excel ----
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Debris Report"

    ws.append(table_headers)
    header_font = Font(bold=True)
    for col in range(1, len(table_headers) + 1):
        ws.cell(row=1, column=col).font = header_font
        ws.cell(row=1, column=col).alignment = Alignment(horizontal="center")
    for row in table_data[1:]:
        ws.append(row)

    # Highlight duplicate tickets
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=2, max_col=2):
        for cell in row:
            if ticket_occurrences[cell.value] > 1:
                cell.font = Font(bold=True, color="FF0000")

    # Summary section
    summary_start = ws.max_row + 2
    summary_data = [
        ("Total Yards:", f"{total_yardage:.2f}"),
        ("Pay Rate:", f"${pay_rate:.2f}"),
        ("Gross Pay:", f"${total_payment:.2f}"),
        ("Retainage (10%):", f"-${retainage:.2f}"),
        ("Net Pay:", f"${net_pay:.2f}")
    ]
    for row in summary_data:
        ws.append(row)

    # Auto-adjust column widths
    for col in ws.columns:
        max_length = 0
        col_letter = col[0].column_letter
        for cell in col:
            if cell.value:
                max_length = max(max_length, len(str(cell.value)))
        ws.column_dimensions[col_letter].width = max_length + 2

    wb.save(excel_buffer)
    excel_buffer.seek(0)

    print(f"[DEBUG] Total tickets in report: {tickets.count()}")
    for t in tickets:
        print(f"Ticket: {t.ticket_no} | Late: {t.is_late}")

    return {
        "pdf": pdf_buffer,
        "excel": excel_buffer
    }




def generate_debris_report(request, debris_area_id):
    from reportlab.platypus import Table, TableStyle, Image
    from reportlab.lib.pagesizes import letter
    from reportlab.lib import colors
    from reportlab.lib.units import inch

    debris_area = get_object_or_404(DebrisArea, id=debris_area_id)
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date or not end_date:
        return JsonResponse({'error': 'Start date and end date are required.'}, status=400)

    try:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return JsonResponse({'error': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

    tickets = Ticket.objects.filter(
        debris_area=debris_area,
        Ticket_date__range=(start_date, end_date)
    )

    buffer = io.BytesIO()
    pdf = canvas.Canvas(buffer, pagesize=letter)
    pdf.setTitle(f"Debris Report for {debris_area.area}")

    def draw_header():
        pdf.setFont("Helvetica-Bold", 14)
        pdf.drawString(50, 750, f"Debris Report for {debris_area.area}")
        pdf.setFont("Helvetica", 10)
        pdf.drawString(50, 735, f"Generated by: {request.user}")
        pdf.drawString(50, 720, f"Period: {start_date} to {end_date}")
        pdf.drawString(50, 705, f"Number of Tickets: {tickets.count()}")

    def draw_table(data):
        table = Table(data, colWidths=[60, 60, 60, 80, 60, 60, 150])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
        ]))
        table.wrapOn(pdf, 50, 400)
        table.drawOn(pdf, 50, 200)

    table_headers = ['Date', 'Ticket #', 'Truck #', 'Debris Type', 'Load %', 'Yards', 'Image']
    data = [table_headers]

    for ticket in tickets:
        image_path = ticket.image_id.images.path if ticket.image_id and ticket.image_id.images else None
        if image_path and os.path.exists(image_path):
            img = Image(image_path)
            img.drawHeight = 1.0 * inch
            img.drawWidth = 1.0 * inch * img.imageWidth / img.imageHeight
        else:
            img = "No Image Available"
        data.append([
            ticket.Ticket_date.strftime('%Y-%m-%d'),
            ticket.ticket_no,
            ticket.Truck_No.truck_number if ticket.Truck_No else "N/A",
            ticket.debris_type.debris_type if ticket.debris_type else "Unknown",
            f"{ticket.load_call}",
            f"{ticket.total_yardage} yd³",
            img
        ])

    rows_per_page = 15
    for i in range(0, len(data), rows_per_page):
        if i > 0:
            pdf.showPage()
        draw_header()
        draw_table(data[i:i + rows_per_page])

    pdf.save()
    buffer.seek(0)
    return FileResponse(buffer, as_attachment=True, filename=f"debris_report_{debris_area.area}.pdf")


@csrf_exempt
def generate_report_view(request, report_id):
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            debris_area_ids = data.get("debris_area_ids", [])
            start_date = data.get("start_date")
            end_date = data.get("end_date")
            pay_rate = data.get("pay_rate")  # Ensure pay_rate is included
            custom_debris_area_name = data.get("custom_debris_area_name")
            generate_pdf   = data.get("generate_pdf", True)
            generate_excel = data.get("generate_excel", True)

            if not start_date or not end_date or not debris_area_ids:
                return JsonResponse({"error": "Missing required fields."}, status=400)

            if int(report_id) not in [int(id) for id in debris_area_ids]:
                return JsonResponse({"error": "Invalid report ID."}, status=400)

            if not pay_rate:
                return JsonResponse({"error": "Pay rate is required."}, status=400)

            if not request.user.is_authenticated:
                return JsonResponse({"error": "User not authenticated."}, status=403)

            user_id = request.user.id

            generate_pdf   = data.get("generate_pdf", True)
            generate_excel = data.get("generate_excel", True)
            task = generate_debris_report_task.apply_async(
                args=[
                    debris_area_ids,
                    start_date,
                    end_date,
                    pay_rate,
                    custom_debris_area_name,
                    user_id
                ],
                kwargs={
                    "generate_pdf": generate_pdf,
                    "generate_excel": generate_excel
                }
            )
            return JsonResponse({"task_id": task.id, "status": "pending"})

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

    return JsonResponse({"error": "Invalid request method."}, status=405)


def check_report_status(request, task_id):
    result = AsyncResult(task_id)
    if result.state == "SUCCESS":
        return JsonResponse({"status": "completed", "file_url": result.result["file_url"]})
    elif result.state == "PENDING":
        return JsonResponse({"status": "pending"})
    elif result.state == "FAILURE":
        return JsonResponse({"status": "failed", "error": str(result.result)})
    else:
        return JsonResponse({"status": result.state})


def generate_truck_report(request, pk):
    page_count = 1
    truck = Truck.objects.get(id=pk)
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if start_date and end_date:
        start_date = datetime.strptime(str(start_date), '%Y-%m-%d').date()
        end_date = datetime.strptime(str(end_date), '%Y-%m-%d').date()

    buffer = io.BytesIO()
    canv = canvas.Canvas(buffer, pagesize=letter)

    data = truck.get_report_data(start_date, end_date)

    report_title = "Truck Report"
    filename = f"{truck.truck_number}_truck_report.pdf"
    table_headers = ['Date', 'Ticket#', 'Debris Type', 'Load %', 'Yardage']

    table_data = [table_headers]

    for ticket in data['tickets']:
        table_data.append([ticket['Date'], ticket['Ticket_No'], ticket['Debris_Type'],
                           ticket['Load_Call'], ticket['Yardage']])

    total_rows = len(table_data)
    rows_on_page = 30
    total_pages = (total_rows - 2) // (rows_on_page - 1) + 1
    x, y = 40, 130
    i = 1
    while i < total_rows:
        if page_count > 1:
            canv.showPage()

        canv.setFont("Helvetica-Bold", 16)
        canv.drawString(200, 770, report_title)
        canv.setFont("Helvetica", 10)
        canv.drawString(50, 750, f"Truck Number: {data['truck_number']}")
        canv.drawString(50, 740, f"Total Yardage: {data['total_yardage']}")
        canv.drawString(50, 730, f"Ticket Count: {data['ticket_count']}")
        canv.drawString(50, 720, f"Driver: {data['sub_contractor']['name']}")
        canv.drawString(500, 750, f"Page {page_count} of {total_pages}")
        canv.drawString(50, 700, f"Period: {start_date} to {end_date}")

        current_table_data = [table_headers] + table_data[i:i + (rows_on_page - 1)]

        table = Table(current_table_data, colWidths=[100, 120, 100, 100, 50, 70])
        styles = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]

        for row_idx, row in enumerate(current_table_data[1:], start=1):  # Skip header row (start at 1)
            if row[4] == 0:
                styles.append(('BACKGROUND', (0, row_idx), (-1, row_idx), colors.red))

        table.setStyle(TableStyle(styles))

        table.wrapOn(canv, 450, 400)
        table.drawOn(canv, x, y)

        i += rows_on_page - 1
        page_count += 1

    canv.save()
    buffer.seek(0)

    return FileResponse(buffer, as_attachment=True, filename=filename)


def debris_report_view(request, pk):
    return generate_subcontractor_report(request, pk)


def late_debris_report_view(request, pk):
    return generate_late_ticket_report(request, pk)


def hanger_report_view(request, pk):
    return generate_late_ticket_report(request, pk)


############################
#### ASYNC UPDATE VIEWS ####
############################
def tickets(request):
    data = dict()
    filter_params = request.session.get('truck_filter', {})
    queryset = Ticket.objects.filter(author=request.user)
    filter = TruckFilter(filter_params, queryset=queryset)

    # Persist the current filter state
    if request.method == 'POST':
        request.session['truck_filter'] = filter.data

    queryset = filter.qs
    paginator = Paginator(queryset, 15)
    page = request.GET.get('page')
    context = paginator.get_page(page)

    if request.method == 'GET':
        data['ticket-table'] = render_to_string(
            'ticket_app/tickets/_ticket_table.html',
            {'page_obj': context, 'filters': filter.data},
            request=request
        )
        return JsonResponse(data)


def hangar_tickets(request):
    data = dict()
    queryset = HangarTicket.objects.filter(author=request.user)
    filter_params = request.session.get('truck_filter', {})
    filter = TruckFilter(filter_params, queryset=queryset)
    request.session['truck_filter'] = filter.data
    queryset = filter.qs
    paginator = Paginator(queryset, 15)
    page = request.GET.get('page')
    context = paginator.get_page(page)
    if request.method == 'GET':
        data['ticket-table'] = render_to_string(
            'ticket_app/hangar_tickets/hanger_ticket_table.html',
            {'page_obj': context},
            request=request
        )
        return JsonResponse(data)


def contractors(request):
    data = dict()
    queryset = Contractor.objects.filter(user=request.user)
    paginator = Paginator(queryset, 15)
    page = request.GET.get('page')
    context = paginator.get_page(page)

    if request.method == 'GET':
        data['contractor-table'] = render_to_string(
            'ticket_app/contractors/_contractor_table.html',
            {'page_obj': context},
            request=request
        )
        return JsonResponse(data)


def sub_contractors(request):
    data = dict()
    queryset = Contractor.objects.filter(user=request.user)
    paginator = Paginator(queryset, 15)
    page = request.GET.get('page')
    context = paginator.get_page(page)

    if request.method == 'GET':
        data['contractor-table'] = render_to_string(
            'ticket_app/subcontractors/sub_contractor_table.html',
            {'page_obj': context},
            request=request
        )
        return JsonResponse(data)


def debris(request):
    data = dict()
    queryset = DebrisArea.objects.filter(user=request.user)
    paginator = Paginator(queryset, 15)
    page = request.GET.get('page')
    context = paginator.get_page(page)

    if request.method == 'GET':
        data['project-table'] = render_to_string(
            'ticket_app/debris/_debris_areas.html',
            {'page_obj': context},
            request=request
        )
        return JsonResponse(data)


def trucks(request):
    data = dict()
    queryset = Truck.objects.filter(user=request.user)
    paginator = Paginator(queryset, 10)
    page = request.GET.get('page')
    context = paginator.get_page(page)
    if request.method == 'GET':
        data['truck-table'] = render_to_string(
            'ticket_app/trucks/_truck_table.html',
            {'page_obj': context},
            request=request
        )
        return JsonResponse(data)


def register(request):
    if request.method == 'POST':
        form = CreateUserForm(request.POST, request=request)
        if form.is_valid():
            form.save()
            messages.success(request, 'Account was created for ' + form.cleaned_data['email'])
            return redirect('login')
    else:
        form = CreateUserForm(request=request)
    return render(request, 'auth/user_form.html', {'form': form})


####################################################
#############  PAYMENT VIEW METHODS ################
####################################################


def thanks(request):
    return render(request, 'thanks.html')


@csrf_exempt
def checkout_enterprise(request):
    stripe.api_key = settings.STRIPE_PRIVATE_KEY

    session = stripe.checkout.Session.create(
        payment_method_types=['card'],
        line_items=[{
            'price': 'price_1MbYjAFisgUKfrlkBi9tzynY',
            'quantity': 1,
        }],
        mode='subscription',
        success_url=request.build_absolute_uri(reverse('home')) + '?session_id={CHECKOUT_SESSION_ID}',
        cancel_url=request.build_absolute_uri(reverse('home')),
    )

    return JsonResponse({
        'session_id': session.id,
        'stripe_public_key': settings.STRIPE_PUBLIC_KEY
    })


@csrf_exempt
def stripe_webhook_enterprise(request):
    print('WEBHOOK!')
    endpoint_secret = config('ENDPOINT_SECRET')

    payload = request.body
    sig_header = request.META['HTTP_STRIPE_SIGNATURE']
    event = None

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError as e:
        # Invalid payload
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        return HttpResponse(status=400)

    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        subscription = stripe.Subscription.retrieve(session['subscription'])
        customer = stripe.Customer.retrieve(subscription.customer)

        user, created = TicketUser.objects.get_or_create(email=customer.email)
        print(user)
        print(customer)
        enterprise_group = Group.objects.get(name='Enterprise')
        print(enterprise_group)
        user.groups.add(enterprise_group)
        user.save()

    return HttpResponse(status=200)


import stripe
from django.conf import settings
from django.shortcuts import render, redirect
from .models import RegistrationToken

stripe.api_key = settings.STRIPE_PRIVATE_KEY


def register_with_token(request, token):
    try:
        reg_token = RegistrationToken.objects.get(token=token)
    except RegistrationToken.DoesNotExist:
        # Handle invalid token
        return render(request, 'auth/invalid_token.html')

    if reg_token.is_used:
        # Handle already used token
        return render(request, 'auth/token_used.html')

    if request.method == "POST":
        form = CreateUserForm(request, data=request.POST)
        if form.is_valid():
            # Save the user and mark the token as used
            user = form.save()
            reg_token.is_used = True
            reg_token.save()

            # Create a Stripe customer
            stripe_customer = stripe.Customer.create(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
            )

            user.stripe_customer_id = stripe_customer['id']
            user.save()

            session = stripe.billing_portal.Session.create(
                customer=user.stripe_customer_id,
                return_url=request.build_absolute_uri('/'),
            )

            return redirect(session.url)

    else:
        form = CreateUserForm(request)

    return render(request, 'auth/user_form.html', {'form': form})


def get_customer_portal_url(customer_id, return_url):
    session = stripe.billing_portal.Session.create(
        customer=customer_id,
        return_url=return_url
    )
    return session.url


def send_customer_portal_link_email(customer_email, portal_url):
    subject = "Manage Your Account Easily with Your Customer Portal"
    message = (
        "Hi there,\n\n"
        "We're always looking for ways to improve your experience with us. To help you manage your account "
        "effortlessly,"
        "we're giving you access to your Customer Portal.\n\n"
        "The Customer Portal is a secure, self-service platform where you can update your billing details, view past "
        "payments,"
        "manage subscriptions, and more—all powered by Stripe. It's designed to put you in control of your account, "
        "anytime, anywhere.\n\n"
        f"Click the link below to get started:\n{portal_url}\n\n"
        "If you have any questions or need help, we're just an email away.\n\n"
        "Thanks for choosing us!\n\n"
        "Best regards,\n"
        "Ticket Pros"
    )
    from_email = "<EMAIL>"
    recipient_list = [customer_email]
    send_mail(subject, message, from_email, recipient_list)


def email_customer_portal_link(customer_id, customer_email, return_url):
    portal_url = get_customer_portal_url(customer_id, return_url)
    send_customer_portal_link_email(customer_email, portal_url)


def is_task_running(request):
    user = request.user
    task_running = UserTaskLock.objects.filter(user=user).exists()
    return JsonResponse({'task_running': task_running})


def user_profile(request):
    user = request.user
    password_form = PasswordChangeForm(user)
    subuser_form = SubUserForm()

    if request.method == 'POST':
        form = TicketUserProfileForm(request.POST, instance=user)

        if 'update_profile' in request.POST:
            if form.is_valid():
                form.save()
                messages.success(request, 'Profile updated successfully')
                return redirect('user_profile')

        elif 'change_password' in request.POST:
            password_form = PasswordChangeForm(user, request.POST)
            if password_form.is_valid():
                user = password_form.save()
                update_session_auth_hash(request, user)
                messages.success(request, 'Password changed successfully')
                return redirect('user_profile')

        elif 'create_subuser' in request.POST:
            if user.role != 'master':
                messages.error(request, 'You do not have permission to create sub-users.')
                return redirect('user_profile')

            subuser_form = SubUserForm(request.POST)
            if subuser_form.is_valid():
                subuser = subuser_form.save(commit=False, master=user)
                subuser.role = 'sub'
                subuser.master_account = user
                subuser.save()

                try:
                    subuser_group = Group.objects.get(name='Enterprise')
                    subuser.groups.add(subuser_group)
                except Group.DoesNotExist:
                    messages.warning(request, "Group 'Enterprise' does not exist. Please create it in the admin.")

                messages.success(request, f'Sub-user {subuser.username} created successfully!')
                return redirect('user_profile')

    else:
        form = TicketUserProfileForm(instance=user)

    sub_users = user.sub_accounts.all()

    context = {
        'form': form,
        'password_form': password_form,
        'subuser_form': subuser_form,
        'sub_users': sub_users
    }
    return render(request, 'ticket_app/user_profile/user_profile.html', context)

import stripe
from django.conf import settings


def update_card(request):
    if request.method == 'POST':
        token = request.POST['stripeToken']

        if not request.user.stripe_customer_id:
            customer = stripe.Customer.create(source=token, email=request.user.email)
            request.user.stripe_customer_id = customer.id
            request.user.save()
        else:
            customer = stripe.Customer.modify(request.user.stripe_customer_id, source=token)

    return render(request)


stripe.api_key = settings.STRIPE_PRIVATE_KEY


def update_payment(request):
    user = request.user
    stripe_customer_id = user.stripe_customer_id

    session = stripe.billing_portal.Session.create(
        customer=stripe_customer_id,
        return_url="https://ticketpros.io/"
    )

    return redirect(session.url)


def create_setup_intent(request, user_id):
    user = get_object_or_404(TicketUser, id=user_id)

    # Create a SetupIntent
    intent = stripe.SetupIntent.create(
        customer=user.stripe_customer_id
    )

    return JsonResponse({
        'clientSecret': intent.client_secret
    })


def test_404(request):
    return HttpResponseNotFound(render(request, '404.html'))


def test_403(request):
    return HttpResponseForbidden(render(request, '403.html'))


def test_400(request):
    return HttpResponseBadRequest(render(request, '400.html'))


def test_500(request):
    return HttpResponseServerError(render(request, '500.html'))


@require_http_methods(["GET"])
def get_messages(request):
    return TemplateResponse(request, 'ticket_app/base/messages.html', {})


@login_required
def list_sub_users(request):
    sub_users = request.user.sub_accounts.all()
    return render(request, 'subusers/list.html', {'sub_users': sub_users})


@login_required
def create_sub_user(request):
    if request.method == 'POST':
        form = SubUserForm(request.POST)
        if form.is_valid():
            form.save(master=request.user)
            return redirect('user_profile')
    else:
        form = SubUserForm()
    return render(request, 'subusers/create.html', {'form': form})


@login_required
def delete_sub_user(request, user_id):
    user = get_object_or_404(TicketUser, pk=user_id, master_account=request.user)
    user.delete()
    return redirect('user_profile')


@login_required
def impersonate_user(request, user_id):
    subuser = get_object_or_404(TicketUser, id=user_id, role='sub', master_account=request.user)
    request.session['impersonate_id'] = subuser.id
    return redirect('yardage_chart')  # or any page you want to land on


@login_required
def stop_impersonation(request):
    if 'impersonate_id' in request.session:
        del request.session['impersonate_id']
    return redirect('user_profile')


def compare_excels(request):
    result_rows = None
    columns = []
    if request.method == "POST":
        form = ExcelCompareForm(request.POST, request.FILES)
        if form.is_valid():
            # read both into DataFrames
            df1 = pd.read_excel(request.FILES['left_file'])
            df2 = pd.read_excel(request.FILES['right_file'])

            # detect your key columns (or hard‑code names)
            ticket1 = next(c for c in df1.columns if 'ticket no' in c.lower())
            load1 = next(c for c in df1.columns if 'load %' in c.lower())
            ticket2 = next(c for c in df2.columns if 'ticket #' in c.lower())
            load2 = next(c for c in df2.columns if 'load %' in c.lower())

            # unify names for ease of merge
            df2 = df2.rename(columns={ticket2: ticket1, load2: load1})

            # make sets of tuples
            set1 = set(zip(df1[ticket1], df1[load1]))
            set2 = set(zip(df2[ticket1], df2[load1]))
            all_pairs = sorted(set1.union(set2), key=lambda x: (str(x[0]), str(x[1])))

            # build a list of dicts for the template
            result_rows = []
            for ticket_val, load_val in all_pairs:
                if (ticket_val, load_val) in set1 and (ticket_val, load_val) in set2:
                    status = 'match'
                else:
                    status = 'mismatch'
                result_rows.append({
                    'ticket': ticket_val,
                    'load': str(load_val).replace('%', ''),
                    'status': status
                })

            columns = [ticket1, load1]

    else:
        form = ExcelCompareForm()

    return render(request, "ticket_app/compare.html", {
        "form": form,
        "rows": result_rows,
        "cols": columns,
    })

