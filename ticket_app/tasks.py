import re
import folium
import regex
import cv2
import numpy as np
import pytesseract
from celery_progress.backend import ProgressRecorder
from django.core.exceptions import ObjectDoesNotExist
import boto3
from django.db.models import Sum
from ticket_app.models import TicketImage, HangarTicket, Truck, HangarTicketImage, \
    UserTaskLock, DebrisType, SubcontractorProject
from pyzbar.pyzbar import decode
from fuzzywuzzy import fuzz, process
import csv
import json
from ticket_app.models import DebrisArea, Ticket
from reportlab.platypus import Image
from django.conf import settings
import os
from datetime import timedelta
import stripe
from django.utils import timezone
from celery import shared_task
import logging
from ticket_app.models import TicketUser
from PIL import Image as PILImage
from decimal import Decimal
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.platypus import Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.utils import <PERSON>Reader
from pathlib import Path
from io import BytesIO
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from collections import Counter

logger = logging.getLogger(__name__)

pytesseract.tesseract_cmd = r'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'



def extract_qr_code_data(image_data):
    logger.info("=== INSIDE extract_qr_code_data FUNCTION ===")
    logger.info(f"Image data size: {len(image_data)} bytes")

    try:
        # Convert the raw image bytes to a NumPy array
        image_array = np.frombuffer(image_data, np.uint8)
        logger.info(f"Image array shape: {image_array.shape}")

        # Decode the image using OpenCV (try both color and grayscale)
        image_color = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
        image_gray = cv2.imdecode(image_array, cv2.IMREAD_GRAYSCALE)

        logger.info(f"Color image shape: {image_color.shape if image_color is not None else 'None'}")
        logger.info(f"Grayscale image shape: {image_gray.shape if image_gray is not None else 'None'}")

        # Check if the image was loaded correctly
        if image_gray is None:
            logger.error("Failed to load the image for QR code extraction.")
            raise ValueError("Failed to load the image for QR code extraction.")

        # Try multiple image preprocessing strategies
        images_to_try = []

        # Strategy 1: Original grayscale
        images_to_try.append(("original_grayscale", image_gray))

        # Strategy 2: Enhanced contrast
        enhanced = cv2.equalizeHist(image_gray)
        images_to_try.append(("enhanced_contrast", enhanced))

        # Strategy 3: Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(image_gray, (3, 3), 0)
        images_to_try.append(("gaussian_blur", blurred))

        # Strategy 4: Morphological operations
        kernel = np.ones((2, 2), np.uint8)
        morph = cv2.morphologyEx(image_gray, cv2.MORPH_CLOSE, kernel)
        images_to_try.append(("morphological", morph))

        # Strategy 5: Adaptive threshold
        try:
            adaptive_thresh = cv2.adaptiveThreshold(image_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            images_to_try.append(("adaptive_threshold", adaptive_thresh))
        except Exception as e:
            logger.warning(f"Adaptive threshold failed: {e}")

        # Strategy 6: Binary threshold
        try:
            _, binary_thresh = cv2.threshold(image_gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            images_to_try.append(("binary_threshold", binary_thresh))
        except Exception as e:
            logger.warning(f"Binary threshold failed: {e}")

        # Strategy 7: Inverted binary threshold
        try:
            _, inv_binary = cv2.threshold(image_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            images_to_try.append(("inverted_binary", inv_binary))
        except Exception as e:
            logger.warning(f"Inverted binary threshold failed: {e}")

        # Strategy 8: Different scales (resize image)
        try:
            # Scale up 2x
            height, width = image_gray.shape
            scaled_up = cv2.resize(image_gray, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)
            images_to_try.append(("scaled_up_2x", scaled_up))

            # Scale down 0.5x
            scaled_down = cv2.resize(image_gray, (width // 2, height // 2), interpolation=cv2.INTER_AREA)
            images_to_try.append(("scaled_down_0.5x", scaled_down))
        except Exception as e:
            logger.warning(f"Scaling failed: {e}")

        logger.info(f"Trying {len(images_to_try)} different image preprocessing strategies...")

        # Try each preprocessing strategy
        for strategy_name, processed_image in images_to_try:
            logger.info(f"\n--- Trying strategy: {strategy_name} ---")

            try:
                # Decode the QR code/barcode in the processed image
                barcodes = decode(processed_image)
                logger.info(f"Strategy '{strategy_name}': Found {len(barcodes)} barcodes")

                # Process the decoded barcodes
                for i, barcode in enumerate(barcodes):
                    logger.info(f"  Barcode {i+1} (Strategy: {strategy_name}):")
                    logger.info(f"    Type: {barcode.type}")
                    logger.info(f"    Raw data: {barcode.data}")
                    logger.info(f"    Raw data type: {type(barcode.data)}")
                    logger.info(f"    Position: {barcode.rect}")

                    try:
                        barcode_data = barcode.data.decode("utf-8")
                        logger.info(f"    Decoded data: '{barcode_data}'")
                        logger.info(f"    Decoded data length: {len(barcode_data)}")
                        logger.info(f"*** SUCCESS with strategy '{strategy_name}' ***")
                        return barcode_data
                    except UnicodeDecodeError as e:
                        logger.error(f"    Unicode decode error: {e}")
                        # Try with different encoding
                        try:
                            barcode_data = barcode.data.decode("latin-1")
                            logger.info(f"    Decoded with latin-1: '{barcode_data}'")
                            logger.info(f"*** SUCCESS with strategy '{strategy_name}' (latin-1) ***")
                            return barcode_data
                        except Exception as e2:
                            logger.error(f"    Failed to decode with latin-1: {e2}")

            except Exception as strategy_error:
                logger.error(f"Strategy '{strategy_name}' failed: {strategy_error}")
                continue

        logger.warning("All preprocessing strategies failed to find barcodes")

        # Save debug images for manual inspection (optional)
        try:
            import os
            debug_dir = "/tmp/barcode_debug"
            os.makedirs(debug_dir, exist_ok=True)

            # Save a few key processed images for debugging
            debug_images = [("original", image_gray), ("enhanced_contrast", enhanced), ("binary_threshold", binary_thresh)]
            for name, img in debug_images:
                if img is not None:
                    debug_path = os.path.join(debug_dir, f"debug_{name}_{hash(str(image_data))}.png")
                    cv2.imwrite(debug_path, img)
                    logger.info(f"Saved debug image: {debug_path}")
        except Exception as debug_error:
            logger.warning(f"Failed to save debug images: {debug_error}")

        # Final attempt: Try with color image if available
        if image_color is not None:
            logger.info("\n--- Final attempt with color image ---")
            try:
                barcodes = decode(image_color)
                logger.info(f"Color image: Found {len(barcodes)} barcodes")

                for i, barcode in enumerate(barcodes):
                    logger.info(f"  Color Barcode {i+1}:")
                    logger.info(f"    Type: {barcode.type}")
                    logger.info(f"    Raw data: {barcode.data}")

                    try:
                        barcode_data = barcode.data.decode("utf-8")
                        logger.info(f"    Decoded data: '{barcode_data}'")
                        logger.info(f"*** SUCCESS with color image ***")
                        return barcode_data
                    except UnicodeDecodeError:
                        try:
                            barcode_data = barcode.data.decode("latin-1")
                            logger.info(f"    Decoded with latin-1: '{barcode_data}'")
                            logger.info(f"*** SUCCESS with color image (latin-1) ***")
                            return barcode_data
                        except Exception:
                            continue
            except Exception as color_error:
                logger.error(f"Color image attempt failed: {color_error}")

        logger.info("No barcodes found with any strategy")
        return None

    except Exception as e:
        logger.error(f"Exception in extract_qr_code_data: {e}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None


def extract_barcode_opencv(image_data):
    """Alternative barcode detection using OpenCV's built-in detector"""
    logger.info("=== TRYING OPENCV BARCODE DETECTOR ===")

    try:
        # Convert the raw image bytes to a NumPy array
        image_array = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

        if image is None:
            logger.error("Failed to load image for OpenCV barcode detection")
            return None

        # Try OpenCV's barcode detector (if available)
        try:
            # This requires opencv-contrib-python
            detector = cv2.barcode.BarcodeDetector()

            # Handle different OpenCV versions that return different numbers of values
            result = detector.detectAndDecode(image)
            logger.info(f"OpenCV detector returned {len(result)} values: {result}")

            if len(result) == 4:
                # Newer OpenCV versions
                retval, decoded_info, decoded_type, points = result
            elif len(result) == 3:
                # Older OpenCV versions
                retval, decoded_info, decoded_type = result
                points = None
            else:
                logger.warning(f"Unexpected number of return values from OpenCV detector: {len(result)}")
                retval = False
                decoded_info = []
                decoded_type = []

            logger.info(f"OpenCV detector retval: {retval}")
            logger.info(f"OpenCV detector decoded_info: {decoded_info}")
            logger.info(f"OpenCV detector decoded_type: {decoded_type}")

            if retval and decoded_info:
                logger.info(f"OpenCV detector found {len(decoded_info)} barcodes")

                # Handle both single string and list of strings
                if isinstance(decoded_info, str):
                    decoded_info = [decoded_info]
                if isinstance(decoded_type, str):
                    decoded_type = [decoded_type]

                for i, info in enumerate(decoded_info):
                    barcode_type = decoded_type[i] if i < len(decoded_type) else "Unknown"
                    logger.info(f"  OpenCV Barcode {i+1}:")
                    logger.info(f"    Type: {barcode_type}")
                    logger.info(f"    Data: '{info}'")
                    if info and info.strip():
                        logger.info(f"*** SUCCESS with OpenCV detector ***")
                        return info.strip()
            else:
                logger.info("OpenCV detector found no barcodes")

        except AttributeError:
            logger.warning("OpenCV barcode detector not available (requires opencv-contrib-python)")
        except Exception as e:
            logger.error(f"OpenCV barcode detector failed: {e}")

        return None

    except Exception as e:
        logger.error(f"Exception in extract_barcode_opencv: {e}")
        return None


def _mark_ticket_as_failed(ticket_image, error_message):
    """Helper function to safely mark a ticket image as failed"""
    try:
        logger.error(f"Marking ticket image {ticket_image.pk} as failed: {error_message}")
        ticket_image.extraction_failed = True
        ticket_image.extracted = True  # Mark as processed but failed
        ticket_image.save(update_fields=['extraction_failed', 'extracted'])
    except Exception as save_error:
        logger.error(f"Failed to mark ticket image {ticket_image.pk} as failed: {save_error}")


def _safe_get_unknown_debris_type():
    """Safely get or create the Unknown debris type"""
    try:
        return DebrisType.objects.get(debris_type='Unknown')
    except DebrisType.DoesNotExist:
        logger.warning("Unknown debris type not found, creating it")
        try:
            return DebrisType.objects.create(debris_type='Unknown')
        except Exception as e:
            logger.error(f"Failed to create Unknown debris type: {e}")
            return None


def _safe_float_conversion(value, default=0.0):
    """Safely convert a value to float with fallback"""
    if value is None:
        return default
    try:
        # Remove non-numeric characters except decimal point
        if isinstance(value, str):
            cleaned_value = re.sub(r'[^\d.]', '', value)
            if not cleaned_value:
                return default
            return float(cleaned_value)
        return float(value)
    except (ValueError, TypeError):
        return default


def _safe_int_conversion(value, default=0):
    """Safely convert a value to int with fallback"""
    if value is None:
        return default
    try:
        if isinstance(value, str):
            cleaned_value = re.sub(r'[^\d]', '', value)
            if not cleaned_value:
                return default
            return int(cleaned_value)
        return int(float(value))  # Handle cases like "50.0"
    except (ValueError, TypeError):
        return default


@shared_task(bind=True, track_started=True)
def extract_img_text(self, ticket_image_pk, user_id, hauling_behind_id=None, is_late=False):
    progress_recorder = ProgressRecorder(self)
    ticket_images = TicketImage.objects.filter(pk__in=ticket_image_pk)
    progress_recorder.total = len(ticket_images)

    try:
        user = TicketUser.objects.get(id=user_id)
    except TicketUser.DoesNotExist:
        logger.error(f"User with ID {user_id} not found")
        return {"error": "User not found"}

    # Define known debris types for fuzzy matching
    known_debris_types = ["C+D", "Veg/Woody"]
    questionable_ticket = False

    # Safely fetch the default "Unknown" debris type
    unknown_debris_type = _safe_get_unknown_debris_type()
    if unknown_debris_type is None:
        logger.error("Could not get or create Unknown debris type, aborting task")
        return {"error": "Could not initialize Unknown debris type"}

    # Define CSV file path (use a directory with appropriate permissions)
    csv_file_path = os.path.join("/tmp", "extracted_ticket_data.csv")

    # Check if CSV file exists; if not, create it and add the header
    if not os.path.isfile(csv_file_path):
        with open(csv_file_path, mode='w', newline='') as file:
            writer = csv.writer(file)
            # Add header row
            writer.writerow(['ticket_id', 'truck_no', 'ticket_date', 'load_call', 'debris_type', 'image_path', 'extracted_keys_values', 'latitude', 'longitude'])

    # Initialize the folium map
    ticket_map = folium.Map(location=[37.0902, -95.7129], zoom_start=4)  # Default center on the USA

    try:
        # Handle user task lock
        lock = UserTaskLock.objects.get(user=user)
        if lock.timestamp < timezone.now() - timedelta(hours=1):
            lock.delete()
        else:
            raise Exception("You already have a task running. Please wait for it to complete.")
    except ObjectDoesNotExist:
        pass

    UserTaskLock.objects.create(user=user)

    try:
        if user.processed_tickets >= 100 and not user.groups.filter(name='Enterprise').exists():
            raise Exception("You have already processed 100 tickets. Please purchase a subscription plan to continue.")

        # Initialize AWS Textract client with error handling
        try:
            textract_client = boto3.client('textract')
        except Exception as e:
            logger.error(f"Failed to initialize AWS Textract client: {e}")
            return {"error": "Failed to initialize AWS Textract client"}

        for i, ticket_image in enumerate(ticket_images):
            progress_recorder.set_progress(i + 1, progress_recorder.total)

            # Wrap each ticket processing in comprehensive error handling
            try:
                logger.info(f"Processing ticket image {ticket_image.pk} ({i+1}/{len(ticket_images)})")

                # Safely read image data
                try:
                    with ticket_image.images.open('rb') as f:
                        image_data = f.read()

                    if not image_data:
                        _mark_ticket_as_failed(ticket_image, "Image file is empty")
                        continue

                except Exception as e:
                    _mark_ticket_as_failed(ticket_image, f"Failed to read image file: {e}")
                    continue

                # Send the image to AWS Textract for key-value pair extraction with error handling
                try:
                    response = textract_client.analyze_document(
                        Document={'Bytes': image_data},
                        FeatureTypes=["FORMS"]
                    )
                except Exception as e:
                    logger.error(f"AWS Textract failed for image {ticket_image.pk}: {e}")
                    _mark_ticket_as_failed(ticket_image, f"AWS Textract analysis failed: {e}")
                    continue

                # Extract key-value pairs with error handling
                try:
                    key_value_pairs = extract_key_value_pairs(response)
                except Exception as e:
                    logger.error(f"Failed to extract key-value pairs for image {ticket_image.pk}: {e}")
                    _mark_ticket_as_failed(ticket_image, f"Key-value extraction failed: {e}")
                    continue

                # Debugging extracted key-value pairs
                logger.info(f"=== AWS TEXTRACT KEY-VALUE PAIRS ===")
                logger.info(f"Total key-value pairs found: {len(key_value_pairs)}")
                for key, value in key_value_pairs.items():
                    logger.info(f"  '{key}' -> '{value}'")
                logger.info(f"=== END AWS TEXTRACT KEY-VALUE PAIRS ===")

                # Normalize extracted keys with error handling
                try:
                    normalized_keys = {key.lower().replace(":", "").strip(): value for key, value in key_value_pairs.items()}
                    logger.info(f"=== NORMALIZED KEYS ===")
                    for key, value in normalized_keys.items():
                        logger.info(f"  '{key}' -> '{value}'")
                    logger.info(f"=== END NORMALIZED KEYS ===")
                except Exception as e:
                    logger.error(f"Failed to normalize keys for image {ticket_image.pk}: {e}")
                    normalized_keys = {}  # Use empty dict as fallback

                # Direct recovery key lookup with error handling
                ticket_no = None
                try:
                    for recovery_key in ['recovery trac', 'recoverytrac', 'recovery irac', 'recoveryirac']:
                        if recovery_key in normalized_keys:
                            ticket_no = normalized_keys[recovery_key]
                            logger.info(f"Direct recovery key match '{recovery_key}': {ticket_no}")
                            break
                except Exception as e:
                    logger.error(f"Error during recovery key lookup for image {ticket_image.pk}: {e}")

                # Extract load call from key-value pairs with error handling
                load_call = None
                try:
                    load_call = key_value_pairs.get('load call', None) or key_value_pairs.get('PercentFull', None)
                except Exception as e:
                    logger.error(f"Error extracting load call for image {ticket_image.pk}: {e}")

                # Manual mapping for debris types
                debris_type_mappings = {
                'CONSTRUCTION AND DEMOLITION': 'C+D',
                'CONSTRUCTION&DEMOLITION': 'C+D',
                'CONSTRUCTION DEMOLITION': 'C+D',
                'C&D COMPACTED': 'C+D',
                'C D': 'C+D',
                'C&D': 'C+D',
                'CD': 'C+D',
                'CONSTRUCTION/DEMOLITION': 'C+D',
                'VEGETATIVE': 'Veg/Woody',
                'VEG': 'Veg/Woody',
                'WOODY': 'Veg/Woody',
                'VEGETAIVE': 'Veg/Woody',
                'VEGETATION': 'Veg/Woody',
                'VEG/WOODY': 'Veg/Woody',
                'VEGETATIVE/WOODY': 'Veg/Woody',
                'Non Urban Vegetative': 'Veg/Woody',
                'NON URBAN VEGETATIVE': 'Veg/Woody',
                'Urban Veg': 'Veg/Woody',
                'URBAN VEG': 'Veg/Woody',
                "Veg": 'Veg/Woody',
                'Vegetative Mulch': 'Veg/Woody',
                'Non Urban Site Prep': 'Veg/Woody',
                'Non Urban C+D' : 'C+D',
                'Non Urban CD' : 'C+D',
                'NON URBAN CD' : 'C+D',
                'NON URBAN MULCH': "Veg/Woody",
                'Non Urban Mulch': "Veg/Woody",
                'Non urban mulch': 'Veg/Woody',
                'non urban mulch': 'Veg/Woody',
            }


                debris_type_instance = unknown_debris_type  # Default to 'Unknown'

                # If load call is found, normalize the value (remove %, convert to float) with error handling
                if load_call:
                    load_call = _safe_float_conversion(load_call, default=0.0)
                    logger.info(f"Load call converted to: {load_call}")

                # If still not found, extract from raw text using regex with error handling
                if not load_call:
                    try:
                        text = ' '.join([block['Text'] for block in response['Blocks'] if block['BlockType'] == 'LINE'])

                        # Regex pattern to match "Load Call" with optional percentage
                        load_call_pattern = r'(?i)(?:load\s*call|percent\s*full)[:=]?\s*(\d+\.?\d*)\s*%?'

                        load_call_match = re.search(load_call_pattern, text)

                        if load_call_match:
                            load_call = _safe_float_conversion(load_call_match.group(1), default=0.0)
                            logger.info(f"Load Call extracted from text: {load_call}")
                    except Exception as e:
                        logger.error(f"Error extracting load call from text for image {ticket_image.pk}: {e}")

                # If still not found, assign a default value
                if not load_call:
                    load_call = 0.0
                    logger.warning(f"Load Call not found for image ID {ticket_image.pk}, using default value '0'")

                # Extract ticket number from key-value pairs with error handling
                try:
                    ticket_no = key_value_pairs.get('ticket no', None)
                except Exception as e:
                    logger.error(f"Error extracting ticket number from key-value pairs for image {ticket_image.pk}: {e}")
                    ticket_no = None

                # Date extraction with comprehensive error handling
                possible_date_keys = [
                    "disposal date/time", "disposal time", "disposal date",
                    "date of disposal", "disposal time (utc)", "date", "ticket date"
                ]

                ticket_date = None
                try:
                    # Try exact match first
                    for key in possible_date_keys:
                        if key in normalized_keys:
                            ticket_date = normalized_keys[key]
                            logger.info(f"Matched Ticket Date using exact key: {key} -> {ticket_date}")
                            break

                    # If no match found, fall back to fuzzy matching
                    if not ticket_date:
                        try:
                            best_date_key, confidence = process.extractOne("disposal date/time", normalized_keys.keys(),
                                                                           scorer=fuzz.token_sort_ratio)
                            if confidence >= 70:
                                ticket_date = normalized_keys[best_date_key]
                                logger.info(f"Matched Ticket Date using fuzzy match: {best_date_key} -> {ticket_date}")
                            else:
                                logger.warning("No strong match for Ticket Date.")
                        except Exception as e:
                            logger.error(f"Error during fuzzy date matching for image {ticket_image.pk}: {e}")

                    # If no date found, look for DROP OFF date in the raw text
                    if not ticket_date:
                        try:
                            text = ' '.join([block['Text'] for block in response['Blocks'] if block['BlockType'] == 'LINE'])
                            # Look for date after "DROP OFF" header
                            drop_off_pattern = r'DROP OFF.*?Date:\s*(\d{1,2}/\d{1,2}/\d{4}\s+\d{1,2}:\d{2}:\d{2})'
                            drop_off_match = re.search(drop_off_pattern, text, re.IGNORECASE | re.DOTALL)

                            if drop_off_match:
                                ticket_date = drop_off_match.group(1)
                                logger.info(f"Found DROP OFF date: {ticket_date}")
                        except Exception as e:
                            logger.error(f"Error extracting DROP OFF date for image {ticket_image.pk}: {e}")

                except Exception as e:
                    logger.error(f"Error during date extraction for image {ticket_image.pk}: {e}")
                    ticket_date = None

                # Hauling behind project lookup with error handling
                hauling_behind_project = None
                if hauling_behind_id:
                    try:
                        hauling_behind_project = SubcontractorProject.objects.get(id=hauling_behind_id)
                    except SubcontractorProject.DoesNotExist:
                        logger.warning(f"Hauling behind project with ID {hauling_behind_id} does not exist.")
                    except Exception as e:
                        logger.error(f"Error looking up hauling behind project for image {ticket_image.pk}: {e}")

                # Extract truck number from key-value pairs with error handling
                truck_no = None
                try:
                    truck_no = key_value_pairs.get('Truck No.') or key_value_pairs.get('DebrisTech')
                except Exception as e:
                    logger.error(f"Error extracting truck number from key-value pairs for image {ticket_image.pk}: {e}")

                if not truck_no:
                    try:
                        # Normalize the text: Remove extra spaces and unify separators
                        text = ' '.join([block['Text'] for block in response['Blocks'] if block['BlockType'] == 'LINE'])
                        normalized_text = re.sub(r'\s+', ' ', text)  # Replace multiple spaces with a single space
                        logger.info(f"Normalized text: {normalized_text}")

                        # Enhanced regex patterns for truck number extraction
                        truck_no_patterns = [
                            r'\bTruck\s*No\.\s*:\s*([A-Z0-9]{1,8})',  # Matches "Truck No. : 8UUE4YW" or "Truck No. : 800131"
                            r'\bTruck\s*No[:\s]*\s*([A-Z0-9]{1,8})',  # Matches "Truck No 8UUE4YW" or "Truck No: 800131"
                            r'\b(?:Truck|Trk)\s*#?\s*([A-Z0-9]+)',  # Matches "Truck #8UUE4YW" or "Trk 800131"

                            # Certification ID Variations
                            r'\bCertification\s*ID\.\s*:\s*([A-Z0-9-]{1,15})',  # Matches "Certification ID. : 12345-AB"
                            r'\bCertification\s*ID[:\s]*\s*([A-Z0-9-]{1,15})',
                            # Matches "Certification ID 12345-XY" or "Certification ID: ABCD1234"
                            r'\b(?:Cert|Certif|Certification)\s*#?\s*([A-Z0-9-]+)',
                            # Matches "Cert #ABC123" or "Certif 56789-XY"
                        ]

                        for pattern in truck_no_patterns:
                            truck_no_match = regex.search(pattern, normalized_text, regex.IGNORECASE)
                            if truck_no_match:
                                truck_no = truck_no_match.group(1)
                                logger.info(f"Truck number extracted using pattern '{pattern}': {truck_no}")
                                break
                    except Exception as e:
                        logger.error(f"Error during truck number regex extraction for image {ticket_image.pk}: {e}")

                if not truck_no:
                    # Log additional debugging information and mark as failed
                    logger.warning(f"Truck number not found for image {ticket_image.pk}")
                    _mark_ticket_as_failed(ticket_image, "Truck number not found")
                    continue

                # Truck instance lookup with error handling
                truck_instance = None
                try:
                    truck_instance = Truck.objects.get(truck_number=truck_no, user=user)
                except Truck.MultipleObjectsReturned:
                    logger.error(f"Multiple trucks found for truck number {truck_no} and user {user.first_name}.")
                    # Handle the multiple objects case, e.g., take the first one
                    truck_instance = Truck.objects.filter(truck_number=truck_no, user=user).first()
                except Truck.DoesNotExist:
                    logger.error(f"Truck with number {truck_no} not found for user {user.first_name}.")
                    _mark_ticket_as_failed(ticket_image, f"Truck with number {truck_no} not found")
                    continue
                except Exception as e:
                    logger.error(f"Error looking up truck for image {ticket_image.pk}: {e}")
                    _mark_ticket_as_failed(ticket_image, f"Error looking up truck: {e}")
                    continue

                contractor = truck_instance.sub_contractor

                # Process ticket date if available with comprehensive error handling
                if ticket_date:
                    try:
                        # Try multiple date formats
                        date_formats = [
                            '%m/%d/%Y %H:%M:%S',  # Example: 11/12/2024 14:52:25
                            '%m/%d/%Y %H:%M',  # Example: 1/30/2025 21:50
                            '%m/%d/%Y %I:%M %p',  # Example: 11/12/2024 2:52 PM
                            '%m/%d/%Y',  # Example: 11/12/2024
                            '%Y-%m-%d %H:%M:%S',  # Example: 2024-11-12 14:52:25
                            '%Y-%m-%d'  # Example: 2024-11-12
                        ]

                        parsed_date = None
                        for date_format in date_formats:
                            try:
                                parsed_date = datetime.strptime(ticket_date, date_format).date()
                                ticket_date = parsed_date.strftime('%Y-%m-%d')
                                logger.info(f"Formatted Ticket Date: {ticket_date}")
                                break
                            except ValueError:
                                continue

                        if not parsed_date:
                            logger.error(f"Could not parse ticket date '{ticket_date}' for image {ticket_image.pk} - marking as failed")
                            _mark_ticket_as_failed(ticket_image, f"Could not parse ticket date: {ticket_date}")
                            continue

                    except Exception as e:
                        logger.error(f"Error processing ticket date for image {ticket_image.pk}: {e}")
                        ticket_date = None

                # Fallback to image date if ticket date is not available
                if not ticket_date:
                    try:
                        if ticket_image.image_date:
                            ticket_date = ticket_image.image_date.strftime('%Y-%m-%d')
                            logger.warning(f"Ticket date not found, using image date: {ticket_date}")
                        else:
                            logger.error(f"Both extracted ticket date and image date are missing for image ID {ticket_image.pk}")
                            _mark_ticket_as_failed(ticket_image, "No valid date found")
                            continue
                    except Exception as e:
                        logger.error(f"Error processing fallback date for image {ticket_image.pk}: {e}")
                        _mark_ticket_as_failed(ticket_image, f"Error processing date: {e}")
                        continue

                # Extract load call value with additional error handling
                if not load_call:
                    try:
                        text = ' '.join([block['Text'] for block in response['Blocks'] if block['BlockType'] == 'LINE'])
                        load_call_pattern = r'(?i)(?:[LI1lI]oad|Load|I oad|l oad|toad|lpd|load cell|I oad Call|Load Cell)\s*[:=]?\s*((?:50|S0|5O|\d{1,3})\.?\d*)\s*%?'
                        load_call_match = regex.search(load_call_pattern, text)
                        load_call = _safe_float_conversion(load_call_match.group(1), default=0.0) if load_call_match else 0.0
                    except Exception as e:
                        logger.error(f"Error extracting load call from text for image {ticket_image.pk}: {e}")
                        load_call = 0.0

                # PRIORITY 1: Extract ticket number from QR code/barcode (HIGHEST PRIORITY)
                ticket_no = None
                logger.info(f"=== STARTING QR CODE EXTRACTION FOR IMAGE ID {ticket_image.pk} ===")

                try:
                    # Try primary method: pyzbar with error handling
                    qr_data = extract_qr_code_data(image_data)
                    logger.info(f"PYZBAR EXTRACTION RESULT: {repr(qr_data)}")
                    logger.info(f"PYZBAR DATA TYPE: {type(qr_data)}")
                    logger.info(f"PYZBAR DATA LENGTH: {len(qr_data) if qr_data else 'None'}")

                    # If pyzbar failed, try OpenCV detector
                    if not qr_data:
                        logger.info("Pyzbar failed, trying OpenCV barcode detector...")
                        qr_data = extract_barcode_opencv(image_data)
                        logger.info(f"OPENCV EXTRACTION RESULT: {repr(qr_data)}")

                    if qr_data:
                        logger.info(f"RAW BARCODE DATA: '{qr_data}'")

                        # Try multiple regex patterns to extract ticket number
                        patterns_to_try = [
                            (r'\b\d{6,12}\b', "6-12 digits"),
                            (r'\b\d{8,10}\b', "8-10 digits"),
                            (r'\b\d{6,8}\b', "6-8 digits"),
                            (r'\d{6,}', "6+ digits anywhere"),
                            (r'\d+', "any digits")
                        ]

                        for pattern, description in patterns_to_try:
                            try:
                                match = re.search(pattern, qr_data)
                                logger.info(f"Trying pattern '{pattern}' ({description}): {match}")
                                if match:
                                    ticket_no = match.group(0)
                                    logger.info(f"PRIORITY 1 SUCCESS - Using barcode data as ticket number: '{ticket_no}' (pattern: {description})")
                                    break
                            except Exception as e:
                                logger.error(f"Error applying pattern '{pattern}' to QR data for image {ticket_image.pk}: {e}")

                        if not ticket_no:
                            logger.warning(f"Barcode data found but no valid ticket number pattern detected in: '{qr_data}'")
                            # Try to extract any numbers from barcode data for debugging
                            try:
                                all_numbers = re.findall(r'\d+', qr_data)
                                logger.info(f"All numbers found in barcode data: {all_numbers}")
                                # If we have any numbers, use the longest one
                                if all_numbers:
                                    longest_number = max(all_numbers, key=len)
                                    if len(longest_number) >= 6:
                                        ticket_no = longest_number
                                        logger.info(f"PRIORITY 1 FALLBACK - Using longest number from barcode: '{ticket_no}'")
                            except Exception as e:
                                logger.error(f"Error extracting numbers from QR data for image {ticket_image.pk}: {e}")
                    else:
                        logger.info("No barcode data extracted from image with any method")
                except Exception as e:
                    logger.error(f"ERROR during QR code extraction for image {ticket_image.pk}: {e}")
                    logger.error(f"Exception type: {type(e)}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")

                logger.info(f"=== QR CODE EXTRACTION COMPLETE. TICKET_NO: {repr(ticket_no)} ===")

                # PRIORITY 2: Extract from key-value pairs (only if QR code failed)
                if not ticket_no:
                    try:
                        logger.info("=== QR CODE FAILED, TRYING KEY-VALUE PAIRS ===")
                        ticket_no = key_value_pairs.get('Ticket Number:', None)
                        if ticket_no:
                            logger.info(f"PRIORITY 2 SUCCESS - Using key-value pair 'Ticket Number:' as ticket number: '{ticket_no}'")
                        else:
                            logger.info("Key-value pair 'Ticket Number:' not found")

                        # Try multiple possible key variations
                        if not ticket_no:
                            ticket_no = (
                                    normalized_keys.get('ticket no') or
                                    normalized_keys.get('ticket no.') or
                                    normalized_keys.get('ticket id') or
                                    normalized_keys.get('ticket_number') or
                                    normalized_keys.get('ticket')
                            )
                            if ticket_no:
                                logger.info(f"PRIORITY 2 - Using normalized key-value pair as ticket number: {ticket_no}")

                        # Continue with PRIORITY 2: Additional key-value pair variations
                        if not ticket_no:
                            ticket_no = key_value_pairs.get('TICKET ID', None)  # Match exactly as extracted
                            if ticket_no:
                                logger.info(f"PRIORITY 2 - Using key-value pair 'TICKET ID' as ticket number: {ticket_no}")

                        if not ticket_no:
                            ticket_no = key_value_pairs.get('Ticket No.', None)
                            if ticket_no:
                                logger.info(f"PRIORITY 2 - Using key-value pair 'Ticket No.' as ticket number: {ticket_no}")

                        # If still not found, check for variations of "TICKET ID" using case-insensitive search
                        if not ticket_no:
                            try:
                                normalized_keys_ticket = {key.lower().strip(): value for key, value in key_value_pairs.items()}
                                ticket_no = normalized_keys_ticket.get('ticket id', None)
                                if ticket_no:
                                    logger.info(f"PRIORITY 2 - Using normalized 'ticket id' as ticket number: {ticket_no}")
                            except Exception as e:
                                logger.error(f"Error normalizing ticket keys for image {ticket_image.pk}: {e}")
                    except Exception as e:
                        logger.error(f"Error during key-value ticket extraction for image {ticket_image.pk}: {e}")

                # PRIORITY 3: Extract from raw text using regex patterns
                if not ticket_no:
                    try:
                        text = ' '.join([block['Text'] for block in response['Blocks'] if block['BlockType'] == 'LINE'])

                        # Improved regex to handle OCR errors, misspellings, and formatting issues
                        ticket_no_pattern = (
                            r'\b(?:'
                            r'T[I1]C?K(?:[FPTB3]?T)?|T[I1]KKT|T[I1]CET|T[I1]CKID|T[I1]CETID|T[I1]C?KETD?|'  # Covers OCR errors like "TICKFT", "TICK3T", "TICKBT"
                            r'T[I1]CKET|T[I1]CK(?:ET)?|TKT|TKTNO|TKT_ID|TKT-NO|TKT:ID|TKT#|'  # Common ticket abbreviations
                            r'T[I1]CKET[_\-:\s]*(?:NO|ID|NUMBER|#)?'  # Matches "TICKET ID", "TICKET_NO", "TICKET-NO"
                            r'T[I1]CKFT[_\-:\s]*(?:NO|ID|NUMBER|#)?'  # Matches "TICKET ID", "TICKET_NO", "TICKET-NO"
                            r')\s*[_\-:\s]*'
                            r'([A-Z0-9]{6,12})\b'  # Captures Ticket ID (6-12 alphanumeric characters)
                        )

                        ticket_no_match = re.search(ticket_no_pattern, text, re.IGNORECASE)

                        if ticket_no_match:
                            ticket_no = ticket_no_match.group(1)
                            logger.info(f"PRIORITY 3 - Ticket ID extracted from text regex: {ticket_no}")
                    except Exception as e:
                        logger.error(f"Error during regex ticket extraction for image {ticket_image.pk}: {e}")

                # PRIORITY 4: Recovery text pattern matching
                if not ticket_no:
                    try:
                        # rebuild the full OCR text
                        text = ' '.join(block['Text']
                                        for block in response['Blocks']
                                        if block['BlockType'] == 'LINE')
                        # look for “Recovery” plus any word chars, whitespace, then the 6–12 digit ID
                        m = re.search(r'(?i)recovery\w*\s*([0-9]{6,12})', text)
                        if m:
                            ticket_no = m.group(1)
                            logger.info(f"PRIORITY 4 - Recovery text regex caught ticket_no: {ticket_no}")
                    except Exception as e:
                        logger.error(f"Error during recovery pattern matching for image {ticket_image.pk}: {e}")

                # PRIORITY 5: Generic number pattern matching (last resort before N/A)
                if not ticket_no:
                    try:
                        ticket_no_pattern = r'(\d{3,9}-\d{3,9}-\d{3,9}|\d{3,20})'
                        ticket_no_match = regex.search(ticket_no_pattern, text)
                        if ticket_no_match:
                            ticket_no = ticket_no_match.group(1)
                            logger.info(f"PRIORITY 5 - Generic number pattern caught ticket_no: {ticket_no}")
                    except Exception as e:
                        logger.error(f"Error during generic pattern matching for image {ticket_image.pk}: {e}")

                # FINAL FALLBACK: If still not found, assign default value
                if not ticket_no:
                    ticket_no = "N/A"
                    questionable_ticket = True
                    logger.warning(f"FINAL FALLBACK - Ticket ID not found for image ID {ticket_image.pk}, using default value 'N/A'")

                # FINAL SUMMARY LOG
                logger.info(f"=== TICKET NUMBER EXTRACTION SUMMARY FOR IMAGE {ticket_image.pk} ===")
                logger.info(f"FINAL TICKET NUMBER: '{ticket_no}'")
                logger.info(f"QUESTIONABLE: {questionable_ticket}")
                logger.info(f"=== END TICKET NUMBER EXTRACTION SUMMARY ===")

                # Extract debris type with error handling
                extracted_debris_type = None
                try:
                    # Normalize extracted keys to remove extra spaces and punctuation
                    normalized_keys_debris = {key.lower().replace(":", "").strip(): value for key, value in key_value_pairs.items()}

                    # Possible key variations
                    possible_keys = ["debris type", "material", "debristype", "debris_type", "Material"]

                    best_match = None
                    highest_confidence = 0

                    for key in possible_keys:
                        match, confidence = process.extractOne(key, normalized_keys_debris.keys(), scorer=fuzz.token_sort_ratio)
                        if confidence > highest_confidence:
                            best_match = match
                            highest_confidence = confidence

                    if best_match and highest_confidence >= 70:
                        extracted_debris_type = normalized_keys_debris[best_match]
                        logger.info(f"Extracted debris type using fuzzy match: {extracted_debris_type}")
                    else:
                        extracted_debris_type = None
                        logger.warning("Debris type not found in extracted text.")
                except Exception as e:
                    logger.error(f"Error extracting debris type for image {ticket_image.pk}: {e}")

                debris_type_instance = unknown_debris_type  # Default to 'Unknown' debris type

                if extracted_debris_type:
                    try:
                        extracted_debris_type = extracted_debris_type.strip().upper()

                        # Match using manual mapping
                        debris_type_str = debris_type_mappings.get(extracted_debris_type, None)

                        # First, check for exact match in manual mappings
                        if extracted_debris_type in debris_type_mappings:
                            try:
                                debris_type_instance = DebrisType.objects.get(
                                    debris_type__iexact=debris_type_mappings[extracted_debris_type])
                                logger.info(
                                    f"Manually mapped debris type: '{extracted_debris_type}' -> '{debris_type_instance.debris_type}'")
                            except DebrisType.DoesNotExist:
                                logger.error(f"Manually mapped debris type not found in database: {debris_type_mappings[extracted_debris_type]}")
                                questionable_ticket = True
                        else:
                            # Use fuzzy matching only if no direct match found
                            try:
                                best_match, confidence = process.extractOne(extracted_debris_type, known_debris_types,
                                                                            scorer=fuzz.partial_ratio)

                                if confidence >= 65:  # Lowered threshold
                                    try:
                                        debris_type_instance = DebrisType.objects.get(debris_type__iexact=best_match)
                                        logger.info(
                                            f"Fuzzy matched debris type: '{extracted_debris_type}' -> '{best_match}' with {confidence}% confidence")
                                    except DebrisType.DoesNotExist:
                                        logger.error(
                                            f"Fuzzy matched debris type '{best_match}' not found in database. Marking as questionable.")
                                        questionable_ticket = True
                                else:
                                    logger.warning(
                                        f"Debris type extraction not confident: '{extracted_debris_type}' matched to '{best_match}' with {confidence}% confidence.")
                                    questionable_ticket = True
                            except Exception as e:
                                logger.error(f"Error during fuzzy debris type matching for image {ticket_image.pk}: {e}")
                                questionable_ticket = True
                    except Exception as e:
                        logger.error(f"Error processing extracted debris type for image {ticket_image.pk}: {e}")
                        questionable_ticket = True

                # Additional debris type processing (legacy code - may be redundant)
                if debris_type_str:
                    try:
                        debris_type_instance = DebrisType.objects.get(debris_type__iexact=debris_type_str)
                    except DebrisType.DoesNotExist:
                        logger.error(
                            f"Debris type '{debris_type_str}' not found in the database. Marking as questionable.")
                        questionable_ticket = True
                    except Exception as e:
                        logger.error(f"Error looking up debris type '{debris_type_str}' for image {ticket_image.pk}: {e}")
                        questionable_ticket = True
                else:
                    # Fuzzy match if no manual match found (legacy fallback)
                    try:
                        best_match, confidence = process.extractOne(
                            extracted_debris_type, known_debris_types, scorer=fuzz.token_sort_ratio
                        )
                        if confidence >= 65:
                            try:
                                debris_type_instance = DebrisType.objects.get(debris_type__iexact=best_match)
                            except DebrisType.DoesNotExist:
                                logger.error(
                                    f"Debris type '{best_match}' not found in the database. Marking as questionable.")
                                questionable_ticket = True
                        else:
                            logger.warning(
                                f"Debris type extraction not confident: '{extracted_debris_type}' matched to '{best_match}' with {confidence}% confidence.")
                            questionable_ticket = True
                    except Exception as e:
                        logger.error(f"Error during legacy debris type matching for image {ticket_image.pk}: {e}")
                        questionable_ticket = True

                if not extracted_debris_type:
                    logger.warning(
                        f"No debris type or material extracted for image ID {ticket_image.pk}. Marking as questionable.")
                    questionable_ticket = True

                # Extract GPS coordinates with error handling
                latitude = longitude = None
                try:
                    gps_coordinates = key_value_pairs.get('GPS(Lat, Lng):', None)
                    if not gps_coordinates:
                        gps_coordinates = key_value_pairs.get('GPS(Lat,Lgn):', None)  # Handle alternate key

                    if gps_coordinates:
                        # Replace hyphens with commas if they exist and then attempt to split
                        try:
                            # Check if the coordinate separator is a hyphen or comma
                            if '-' in gps_coordinates and ',' not in gps_coordinates:
                                lat_long = gps_coordinates.split('-')
                            else:
                                lat_long = gps_coordinates.split(',')

                            latitude = _safe_float_conversion(lat_long[0].strip())
                            longitude = _safe_float_conversion(lat_long[1].strip())
                            logger.info(f"Extracted GPS coordinates: Latitude {latitude}, Longitude {longitude}")

                        except (ValueError, IndexError) as e:
                            logger.error(f"Error parsing GPS coordinates '{gps_coordinates}': {e}")
                except Exception as e:
                    logger.error(f"Error extracting GPS coordinates for image {ticket_image.pk}: {e}")

                # Add a marker to the map if GPS coordinates are valid
                try:
                    if latitude is not None and longitude is not None and latitude != 0.0 and longitude != 0.0:
                        folium.Marker(
                            location=[latitude, longitude],
                            popup=f"Ticket ID: {ticket_image.pk}, Truck No: {truck_no}",
                            tooltip="Pickup Location"
                        ).add_to(ticket_map)
                    else:
                        logger.warning(f"No valid GPS coordinates found for image ID {ticket_image.pk}")
                except Exception as e:
                    logger.error(f"Error adding map marker for image {ticket_image.pk}: {e}")

                # Create ticket and save extracted details with comprehensive error handling
                try:
                    # Safely convert load_call to int
                    load_call_int = _safe_int_conversion(load_call, default=0)

                    # Create ticket with error handling
                    ticket = Ticket(
                        Truck_No=truck_instance,
                        ticket_no=ticket_no,
                        Contractor=contractor,
                        debris_type=debris_type_instance,
                        debris_area=ticket_image.debris_area,
                        Ticket_date=ticket_date,
                        load_call=load_call_int,
                        Notes=key_value_pairs,
                        image_id=ticket_image,
                        author=ticket_image.author,
                        questionable=questionable_ticket,
                        hauling_behind=hauling_behind_project,
                        is_late=is_late,
                    )

                    # Save ticket with error handling
                    try:
                        ticket.save()
                        logger.info(f"Successfully created ticket {ticket.ticket_id} for image {ticket_image.pk}")
                    except Exception as save_error:
                        logger.error(f"Error saving ticket for image {ticket_image.pk}: {save_error}")
                        _mark_ticket_as_failed(ticket_image, f"Error saving ticket: {save_error}")
                        continue

                    # Serialize key-value pairs and append data to CSV with error handling
                    try:
                        extracted_keys_values_str = json.dumps(key_value_pairs)
                        with open(csv_file_path, mode='a', newline='') as file:
                            writer = csv.writer(file)
                            writer.writerow([
                                ticket.ticket_id,
                                truck_no,
                                ticket_date,
                                load_call,
                                debris_type_instance.debris_type if debris_type_instance else 'Unknown',
                                ticket_image.images.name,
                                extracted_keys_values_str,
                                latitude,
                                longitude
                            ])
                    except Exception as csv_error:
                        logger.error(f"Error writing to CSV for ticket {ticket.ticket_id}: {csv_error}")
                        # Don't fail the ticket for CSV errors, just log

                    if ticket.questionable:
                        logger.info(f"Ticket ID {ticket.ticket_id} has been marked as questionable.")

                    # Update user stats with error handling
                    try:
                        user.processed_tickets += 1
                        user.tickets_since_last_bill += 1
                        user.save()
                    except Exception as user_save_error:
                        logger.error(f"Error updating user stats for ticket {ticket.ticket_id}: {user_save_error}")
                        # Don't fail the ticket for user stat errors

                    # Mark ticket image as successfully extracted
                    try:
                        ticket_image.extracted = True
                        ticket_image.extraction_failed = False
                        ticket_image.save(update_fields=["extracted", "extraction_failed"])
                        logger.info(f"Successfully processed ticket image {ticket_image.pk}")
                    except Exception as image_save_error:
                        logger.error(f"Error updating ticket image status for {ticket_image.pk}: {image_save_error}")

                except Exception as e:
                    logger.error(f"Error during ticket creation for image ID {ticket_image.pk}: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    _mark_ticket_as_failed(ticket_image, f"Error during ticket creation: {e}")
                    continue

            # End of individual ticket processing - catch any remaining errors
            except Exception as ticket_processing_error:
                logger.error(f"Unexpected error processing ticket image {ticket_image.pk}: {ticket_processing_error}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                _mark_ticket_as_failed(ticket_image, f"Unexpected processing error: {ticket_processing_error}")
                continue

        # Save the folium map to an HTML file
        map_file_path = "/tmp/ticket_pickup_locations_map.html"
        ticket_map.save(map_file_path)
        logger.info(f"Map saved at {map_file_path}")

    finally:
        # Remove user task lock
        UserTaskLock.objects.filter(user=user).delete()


def extract_key_value_pairs(textract_response):
    """
    Extract key-value pairs from the AWS Textract response.
    """
    key_map = {}
    value_map = {}
    block_map = {}

    for block in textract_response['Blocks']:
        block_id = block['Id']
        block_map[block_id] = block
        if block['BlockType'] == 'KEY_VALUE_SET':
            if 'KEY' in block['EntityTypes']:
                key_map[block_id] = block
            elif 'VALUE' in block['EntityTypes']:
                value_map[block_id] = block

    key_value_pairs = {}

    for key_block in key_map.values():
        value_block = find_value_for_key(key_block, value_map, block_map)
        key = get_text_from_block(key_block, block_map)
        value = get_text_from_block(value_block, block_map)
        key_value_pairs[key] = value

    return key_value_pairs


def find_value_for_key(key_block, value_map, block_map):
    """
    Find the value block corresponding to a key block.
    """
    for relationship in key_block.get('Relationships', []):
        if relationship['Type'] == 'VALUE':
            for value_id in relationship['Ids']:
                value_block = value_map.get(value_id)
                if value_block:
                    return value_block
    return None


def get_text_from_block(block, block_map):
    """
    Get the text from a block.
    """
    text = ""
    if 'Relationships' in block:
        for relationship in block['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    word_block = block_map[child_id]
                    if word_block['BlockType'] == 'WORD':
                        text += word_block['Text'] + " "
                    elif word_block['BlockType'] == 'SELECTION_ELEMENT':
                        if word_block['SelectionStatus'] == 'SELECTED':
                            text += "X "
    return text.strip()

@shared_task(bind=True, track_started=True)
def extract_hanger_img_text(self, ticket_image_pk, user_id):
    kernel_size = np.ones((2, 2), np.uint8)
    progress_recorder = ProgressRecorder(self)
    ticket_images = HangarTicketImage.objects.filter(pk__in=ticket_image_pk)
    progress_recorder.total = len(ticket_images)
    # Get the current user and check the number of processed tickets
    user = TicketUser.objects.get(id=user_id)

    # Check if the user already has a task running
    try:
        lock = UserTaskLock.objects.get(user=user)
        raise Exception("You already have a task running. Please wait for it to complete before starting another.")
    except ObjectDoesNotExist:
        pass

    UserTaskLock.objects.create(user=user)
    try:
        if user.processed_tickets >= 100 and not user.groups.filter(name='Enterprise').exists():
            raise Exception("You have already processed 100 tickets. Please purchase a subscription plan to continue.")

        for i, ticket_image in enumerate(ticket_images):
            progress_recorder.set_progress(i, progress_recorder.total)

            # Wrap each hangar ticket processing in comprehensive error handling
            try:
                logger.info(f"Processing hangar ticket image {ticket_image.pk} ({i+1}/{len(ticket_images)})")

                if user.processed_tickets >= 100 and not user.groups.filter(name='Enterprise').exists():
                    raise Exception(
                        "You have already processed 100 tickets. Please purchase a subscription plan to continue.")

                # Safely read image data
                try:
                    with ticket_image.images.open('rb') as f:
                        image_data = f.read()

                    if not image_data:
                        logger.error(f"Image file is empty for hangar ticket {ticket_image.pk}")
                        continue

                except Exception as e:
                    logger.error(f"Failed to read hangar image file {ticket_image.pk}: {e}")
                    continue

                # Safely decode image with OpenCV
                try:
                    image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)
                    if image is None:
                        logger.error(f"Failed to decode hangar image {ticket_image.pk}")
                        continue
                except Exception as e:
                    logger.error(f"Error decoding hangar image {ticket_image.pk}: {e}")
                    continue

                # Safely extract text with Tesseract
                try:
                    text = pytesseract.image_to_string(image, lang="eng",
                                                       config=r"-c tessedit_char_blacklist=!@?#$¢es<>/\|^&*[]")
                    logger.info(f"Extracted text from hangar image {ticket_image.pk}: {len(text)} characters")
                except Exception as e:
                    logger.error(f"Error extracting text from hangar image {ticket_image.pk}: {e}")
                    continue

                # Extract measure (decimal), crew (integer), and unit count (integer) with error handling
                measure = None
                crew = None
                unit_count = None

                try:
                    measure_pattern = r'(?i)(?:measure|m3asur3|Maur|maur|Maaur|Maaur|Maura|mer|Moaur|Moaura|Moagur|Moaura|Moagura|Moagura|Maur:®)\s*[:=]?\s*(\d+\.\d{1,2})'
                    measure_match = regex.search(measure_pattern, text)
                    measure = _safe_float_conversion(measure_match.group(1)) if measure_match else None
                except Exception as e:
                    logger.error(f"Error extracting measure from hangar image {ticket_image.pk}: {e}")

                try:
                    crew_pattern = r'(?e)(?:crew|cr3w|crow){e<=2}\s*[:=]?\s*(\d+)'
                    crew_match = regex.search(crew_pattern, text, regex.IGNORECASE)
                    crew = _safe_int_conversion(crew_match.group(1)) if crew_match else None
                except Exception as e:
                    logger.error(f"Error extracting crew from hangar image {ticket_image.pk}: {e}")

                try:
                    unit_count_pattern = r'(?e)(?:unit\s*count){e<=2}\s*[:=]?\s*(\d+)'
                    unit_count_match = regex.search(unit_count_pattern, text, regex.IGNORECASE)
                    unit_count = _safe_int_conversion(unit_count_match.group(1)) if unit_count_match else None
                except Exception as e:
                    logger.error(f"Error extracting unit count from hangar image {ticket_image.pk}: {e}")

                logger.info(f"Extracted values - Measure: {measure}, Crew: {crew}, Unit Count: {unit_count}")

                # Extract keywords with error handling
                default_values = {'load call': 'N/A', 'ticket no': 'N/A', 'date': 'N/A'}
                kvs = default_values.copy()

                try:
                    keywords = ['program', 'truck no', 'ticket no', 'date', 'driver', 'ticket id', 'Ticket ID', 'disaster',
                                'capacity', 'debris type', 'applicant',
                                'address', 'gps', 'contractor', 'disposal site', 'gps', 'measure']
                    for keyword in keywords:
                        try:
                            # Check if the keyword is in the text
                            if keyword in text.lower():
                                # Extract the line containing the keyword
                                lines = text.split("\n")
                                for line in lines:
                                    if keyword in line.lower():
                                        logger.info(f"Found keyword '{keyword}' in line: {line}")
                                        kvs[keyword] = line
                                        break
                        except Exception as e:
                            logger.error(f"Error processing keyword '{keyword}' for hangar image {ticket_image.pk}: {e}")
                except Exception as e:
                    logger.error(f"Error extracting keywords from hangar image {ticket_image.pk}: {e}")

                # Extract ticket number with error handling
                ticket_no = 'N/A'
                try:
                    if 'ticket no' in kvs:
                        match = regex.search(r'(\d{3,9}-\d{3,9}-\d{3,9}|\d{3,20})', text)
                        if match:
                            ticket_no = match.group(1)
                        else:
                            ticket_no = kvs['ticket no']
                except Exception as e:
                    logger.error(f"Error extracting ticket number from hangar image {ticket_image.pk}: {e}")

                # Extract load percent with error handling
                load_percent = 0
                try:
                    load_pattern = r'(?e)(load percent of truck capacity:){e<=8}\s*(\d+\.?\d+)'
                    load_match = regex.search(load_pattern, text, regex.BESTMATCH)
                    if load_match:
                        load_percent = _safe_float_conversion(load_match.group(2), default=0.0) * 100
                    logger.info(f"Load percent: {load_percent}")
                except Exception as e:
                    logger.error(f"Error extracting load percent from hangar image {ticket_image.pk}: {e}")

                # Extract ticket ID with error handling
                ticket_id = 0
                try:
                    id_pattern = r'(?e)(?:Ticket ID:){e<=3}\s*(\d+\.?\d+)'
                    id_match = regex.search(id_pattern, text, regex.BESTMATCH)
                    if id_match:
                        ticket_id = _safe_float_conversion(id_match.group(1), default=0.0)
                    logger.info(f"Ticket ID: {ticket_id}")
                except Exception as e:
                    logger.error(f"Error extracting ticket ID from hangar image {ticket_image.pk}: {e}")

                # Create hangar ticket with comprehensive error handling
                try:
                    # Safely get debris area
                    debris_area = None
                    try:
                        debris_area = DebrisArea.objects.filter(area=ticket_image.debris_area.area).first()
                    except Exception as e:
                        logger.error(f"Error getting debris area for hangar image {ticket_image.pk}: {e}")

                    # Create hangar ticket
                    ticket = HangarTicket(
                        Truck_No=ticket_image.truck,
                        ticket_no=ticket_no,
                        Contractor=ticket_image.truck.sub_contractor,
                        debris_type=ticket_image.debris_type,
                        debris_area=ticket_image.debris_area,
                        Ticket_date=ticket_image.image_date,
                        Notes=text,
                        image_id=ticket_image,
                        measure=measure,
                        unit_count=unit_count,
                        crew=crew,
                        author=ticket_image.author
                    )

                    # Save ticket with error handling
                    try:
                        ticket.save()
                        logger.info(f"Successfully created hangar ticket for image {ticket_image.pk}")

                        # Mark as questionable if measure is 0
                        if measure == 0 or measure is None:
                            ticket.questionable = True
                            ticket.save()
                            logger.info(f"Hangar ticket {ticket.pk} marked as questionable due to measure: {measure}")

                    except Exception as save_error:
                        logger.error(f"Error saving hangar ticket for image {ticket_image.pk}: {save_error}")
                        continue

                    # Update user stats with error handling
                    try:
                        user.processed_tickets += 1
                        user.tickets_since_last_bill += 1
                        user.save()
                    except Exception as user_save_error:
                        logger.error(f"Error updating user stats for hangar ticket {ticket.pk}: {user_save_error}")
                        # Don't fail the ticket for user stat errors

                    # Mark image as processed
                    try:
                        ticket_image.extracted = True
                        ticket_image.save(update_fields=['extracted'])
                        logger.info(f"Successfully processed hangar ticket image {ticket_image.pk}")
                    except Exception as image_save_error:
                        logger.error(f"Error updating hangar image status for {ticket_image.pk}: {image_save_error}")

                except Truck.DoesNotExist:
                    logger.error(f"Truck does not exist for hangar image {ticket_image.pk}")
                    try:
                        ticket_image.extracted = False
                        ticket_image.save(update_fields=['extracted'])
                    except Exception as e:
                        logger.error(f"Error updating hangar image status after truck error: {e}")

                except Exception as e:
                    logger.error(f"Error during hangar ticket creation for image {ticket_image.pk}: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    try:
                        ticket_image.extracted = False
                        ticket_image.save(update_fields=['extracted'])
                    except Exception as save_error:
                        logger.error(f"Error updating hangar image status after error: {save_error}")

            # End of individual hangar ticket processing - catch any remaining errors
            except Exception as hangar_processing_error:
                logger.error(f"Unexpected error processing hangar ticket image {ticket_image.pk}: {hangar_processing_error}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                try:
                    ticket_image.extracted = False
                    ticket_image.save(update_fields=['extracted'])
                except Exception as save_error:
                    logger.error(f"Error updating hangar image status after unexpected error: {save_error}")
                continue

    finally:
        UserTaskLock.objects.filter(user=user).delete()






@shared_task
def bill_enterprise_users():
    # Define the price ID for the "Basic ticket" product
    ticket_price_id = "price_1PQwaJFisgUKfrlkLTwC0Jue"  # Replace with actual price ID

    # Retrieve the current date
    today = timezone.now().date()
    print(f"Running weekly billing for Enterprise users on {today}")

    # Get all users in the Enterprise group
    enterprise_users = TicketUser.objects.filter(groups__name="Enterprise")

    billed_users_count = 0

    for user in enterprise_users:
        # Calculate the number of tickets since last billed
        tickets_since_last_bill = user.tickets_since_last_bill

        if tickets_since_last_bill > 0:
            try:
                # Create the invoice with auto_advance set to False
                invoice = stripe.Invoice.create(
                    customer=user.stripe_customer_id,
                    collection_method="send_invoice",  # Set to email the invoice
                    days_until_due=30,
                    auto_advance=False  # Manual control for finalizing and sending
                )

                # Create the invoice item and attach it to the created invoice
                stripe.InvoiceItem.create(
                    customer=user.stripe_customer_id,
                    price=ticket_price_id,
                    quantity=tickets_since_last_bill,
                    invoice=invoice.id,  # Attach directly to the created invoice
                    description=f"Basic ticket - {tickets_since_last_bill} tickets"
                )

                print(f"Invoice item created for user {user.id} and attached to invoice {invoice.id}")

                # Finalize the invoice to make it ready for sending
                finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)

                # Explicitly send the invoice email to the customer
                stripe.Invoice.send_invoice(finalized_invoice.id)

                print(f"Invoice sent to user {user.id} for {tickets_since_last_bill} tickets.")
                logger.info(f"Invoice sent to user {user.id} for {tickets_since_last_bill} tickets.")

                # Reset user's ticket count and update last billed date
                user.tickets_since_last_bill = 0
                user.last_billed_date = today
                user.save()

                billed_users_count += 1

            except stripe.error.StripeError as e:
                logger.error(f"Stripe error billing user {user.id}: {e}")
            except Exception as e:
                logger.error(f"Error billing user {user.id}: {e}")

    return {"billed_users_count": billed_users_count}


@shared_task
def bill_enterprise_yardage(start_date=None, end_date=None):
    if not start_date or not end_date:
        today = timezone.now().date()
        start_date = today - timedelta(days=today.weekday())  # Start of the week
        end_date = start_date + timedelta(days=6)

    print(f"Billing Enterprise users from {start_date} to {end_date}")
    billed_users_count = 0

    # Get only master accounts in the Enterprise group
    enterprise_users = TicketUser.objects.filter(groups__name="Enterprise", role="master")

    for master in enterprise_users:
        price_per_yardage = master.price_per_yardage

        sub_users = master.sub_accounts.all()
        all_user_ids = list(sub_users.values_list("id", flat=True)) + [master.id]

        print(f"[DEBUG] Master: {master.email}")
        print(f"[DEBUG] Sub-users: {list(sub_users.values_list('username', flat=True))}")

        unbilled_tickets = Ticket.objects.filter(
            author_id__in=all_user_ids,
            Ticket_date__range=[start_date, end_date],
            billed=False
        )

        print(f"[DEBUG] Total unbilled tickets: {unbilled_tickets.count()}")

        weekly_yardage = unbilled_tickets.aggregate(total_yardage=Sum('total_yardage'))['total_yardage'] or Decimal(0)
        total_cost_cents = int(weekly_yardage * price_per_yardage * Decimal(100))

        if total_cost_cents > 0:
            try:
                invoice = stripe.Invoice.create(
                    customer=master.stripe_customer_id,
                    collection_method="send_invoice",
                    days_until_due=7,
                    auto_advance=False
                )

                stripe.InvoiceItem.create(
                    customer=master.stripe_customer_id,
                    unit_amount=total_cost_cents,
                    currency="usd",
                    quantity=1,
                    invoice=invoice.id,
                    description=f"Yardage - {weekly_yardage} units at ${price_per_yardage}/unit"
                )

                finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)
                stripe.Invoice.send_invoice(finalized_invoice.id)

                print(
                    f"Invoiced master {master.email} for {weekly_yardage} yardage units (${total_cost_cents / 100:.2f})")

                unbilled_tickets.update(billed=True)
                billed_users_count += 1

            except stripe.error.StripeError as e:
                logger.error(f"Stripe error billing user {master.email}: {e}")
            except Exception as e:
                logger.error(f"Error billing user {master.email}: {e}")

    return {"billed_users_count": billed_users_count}



def calculate_rows_per_page(page_height, header_height, row_height, footer_height, data, min_rows=2):
    """
    Dynamically calculate how many rows can fit on a page based on content size,
    ensuring at least min_rows rows per page.
    """
    available_height = page_height - header_height - footer_height
    max_rows = int(available_height / row_height)

    # Adjust for rows with large images
    rows_with_images = [row for row in data if isinstance(row[-1], Image)]  # Check if row has an image
    for _ in rows_with_images:
        max_rows -= 1  # Assume rows with images take more vertical space

    return max(max_rows, min_rows)  # Ensure at least min_rows rows fit on the page


def compress_image(image_path, max_width=400, max_height=300, quality=75):
    """
    Compress and resize an image to fit within max dimensions and save with quality.
    Handles RGBA images by converting to RGB before saving as JPEG.
    """
    with PILImage.open(image_path) as img:
        # Resize the image while maintaining aspect ratio
        img.thumbnail((max_width, max_height))

        # Convert RGBA to RGB if necessary
        if img.mode == "RGBA":
            img = img.convert("RGB")

        buffer = BytesIO()
        img.save(buffer, format="JPEG", quality=quality)
        buffer.seek(0)
        return buffer




@shared_task(bind=True, track_started=True)
def generate_debris_report_task(
    self,
    debris_area_ids,
    start_date,
    end_date,
    pay_rate,
    custom_debris_area_name,
    user_id,
    generate_pdf: bool = True,
    generate_excel: bool = True,
):


    result = {}

    user = TicketUser.objects.get(id=user_id)
    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date   = datetime.strptime(end_date, '%Y-%m-%d').date()
    pay_rate   = Decimal(pay_rate)

    debris_areas = DebrisArea.objects.filter(id__in=debris_area_ids)
    from django.db.models import Q
    all_tickets = Ticket.objects.filter(
        Q(debris_area__in=debris_areas),
        Ticket_date__range=(start_date, end_date)
    )
    regular_tickets = all_tickets.order_by('Ticket_date')

    # detect duplicates…
    ticket_counts = Counter(t.ticket_no for t in all_tickets if t.ticket_no)
    duplicates    = {t for t,c in ticket_counts.items() if c>1}

    # yardage math…
    total_yardage      = sum(t.total_yardage or 0 for t in regular_tickets)
    total_amount       = total_yardage * pay_rate
    retainage          = total_amount * Decimal('0.10')
    total_after_retain = total_amount - retainage

    report_dir = Path(settings.MEDIA_ROOT) / "reports"
    report_dir.mkdir(exist_ok=True, parents=True)

    if generate_pdf:
        # ——— PDF setup ———
        buffer = BytesIO()
        canv   = canvas.Canvas(buffer, pagesize=letter)

        # Header
        canv.setFont("Helvetica", 10)
        canv.drawImage('static/img/medium.png', 25, 725, 100, 100, mask='auto', preserveAspectRatio=True)
        canv.drawString(250, 775, "Debris Report")
        canv.drawString(450, 775, f"Report Created: {datetime.now().date()}")
        canv.drawString(10, 740, f"Debris Area: {custom_debris_area_name}")
        canv.drawString(10, 725, f"Company: {user.company_name or 'N/A'}")
        canv.drawString(10, 710, f"Billing Address: {user.billing_address or 'N/A'}")
        canv.drawString(10, 695, f"{user.billing_city or 'N/A'}, {user.billing_state or 'N/A'}, {user.billing_postal_code or 'N/A'}")
        canv.drawString(450, 740, f"Total Yardage: {total_yardage:.2f} yards")
        canv.drawString(450, 730, f"Pay Rate: ${pay_rate:.2f} per yard")
        canv.drawString(450, 720, f"Total Amount: ${total_amount:.2f}")
        canv.drawString(450, 710, f"Retainage (10%): -${retainage:.2f}")
        canv.drawString(450, 700, f"Total After Retainage: ${total_after_retain:.2f}")
        canv.drawString(450, 765, f"Period: {start_date} to {end_date}")
        canv.line(0, 750, 650, 750)
        canv.line(0, 690, 650, 690)

        table_headers = ['Date', 'Ticket #', 'Truck #', 'Debris Type', 'Load %', 'Yards', 'Type']
        table_data = [table_headers]
        ticket_images = []
        duplicate_rows_pdf = []
        table_rows_with_keys = []

        def add_ticket_row(ticket, ticket_type):
            ticket_no = ticket.ticket_no or "Unknown"
            is_duplicate = ticket_no in duplicates
            row = [
                ticket.Ticket_date.strftime('%Y-%m-%d'),
                ticket_no,
                ticket.Truck_No.truck_number if ticket.Truck_No else "N/A",
                ticket.debris_type.debris_type if ticket.debris_type else "Unknown",
                f"{ticket.load_call}" if ticket.load_call else "0",
                f"{ticket.total_yardage:.2f}" if ticket.total_yardage else "0.00",
                ticket_type
            ]
            table_rows_with_keys.append((row, ticket_no))
            if ticket.image_id and ticket.image_id.images:
                ticket_images.append(ticket.image_id.images.path)

        for ticket in regular_tickets:
            add_ticket_row(ticket, "Regular")

        # Sort and rebuild table_data
        table_data = [table_headers]
        for idx, (row, ticket_no) in enumerate(sorted(table_rows_with_keys, key=lambda x: datetime.strptime(x[0][0], '%Y-%m-%d'))):
            table_data.append(row)
            if ticket_no in duplicates:
                duplicate_rows_pdf.append(idx + 1)  # +1 for header row

        # ===== Render paginated PDF table with yellow highlight =====
        rows_per_page = 30
        i = 1  # Skip header at index 0
        while i < len(table_data):
            page_data = [table_headers] + table_data[i:i + rows_per_page]
            table = Table(page_data, colWidths=[80, 100, 50, 70, 40, 70, 70])
            style = [
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]

            for local_idx, global_idx in enumerate(range(i, min(i + rows_per_page, len(table_data)))):
                if global_idx in duplicate_rows_pdf:
                    style.append(('BACKGROUND', (0, local_idx + 1), (-1, local_idx + 1), colors.yellow))

            table.setStyle(TableStyle(style))
            table.wrapOn(canv, 50, 300)
            table.drawOn(canv, 50, 100)
            canv.showPage()
            i += rows_per_page

        # ===== Ticket images (optional) =====
        image_width, image_height = 600, 800
        x_start, y_start = 5, 10
        x, y = x_start, y_start

        for image_path in ticket_images:
            if image_path and os.path.exists(image_path):
                compressed_image = compress_image(image_path, max_width=image_width, max_height=image_height, quality=75)
                img_reader = ImageReader(compressed_image)
                canv.drawImage(img_reader, x, y, width=image_width, height=image_height)
            x += image_width + 20
            if x + image_width > letter[0]:
                x = x_start
                y -= image_height + 20
            if y - image_height < 50:
                canv.showPage()
                x, y = x_start, y_start

        canv.save()
        buffer.seek(0)

        # save PDF to disk
        pdf_file_path  = report_dir / f"debris_report_{start_date}_{end_date}.pdf"
        with open(pdf_file_path, "wb") as f:
            f.write(buffer.getvalue())
        result['pdf_file_url'] = f"{settings.MEDIA_URL}reports/{pdf_file_path.name}"

    if generate_excel:
        # ——— Excel setup ———
        table_headers = ['Date', 'Ticket #', 'Truck #', 'Debris Type', 'Load %', 'Yards', 'Type']
        # If not already built (from PDF), build table_data here
        table_data = [table_headers]
        ticket_nos = [t.ticket_no for t in regular_tickets if t.ticket_no]
        duplicates = {t for t, c in Counter(ticket_nos).items() if c > 1}
        for ticket in regular_tickets:
            row = [
                ticket.Ticket_date.strftime('%Y-%m-%d'),
                ticket.ticket_no or "Unknown",
                ticket.Truck_No.truck_number if ticket.Truck_No else "N/A",
                ticket.debris_type.debris_type if ticket.debris_type else "Unknown",
                f"{ticket.load_call}" if ticket.load_call else "0",
                f"{ticket.total_yardage:.2f}" if ticket.total_yardage else "0.00",
                "Regular"
            ]
            table_data.append(row)

        wb  = Workbook()
        ws  = wb.active
        ws.title = "Debris Report"
        ws.append(table_headers)
        header_font = Font(bold=True)
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        for col in ws.iter_cols(min_row=1, max_row=1, min_col=1, max_col=len(table_headers)):
            for cell in col:
                cell.font = header_font
                cell.alignment = Alignment(horizontal="center", vertical="center")

        for idx, row in enumerate(table_data[1:], start=2):  # skip header
            ws.append(row)
            ticket_no = row[1]
            if ticket_no in duplicates:
                for cell in ws[idx]:
                    cell.fill = yellow_fill

        xlsx_file_path = report_dir / f"debris_report_{start_date}_{end_date}.xlsx"
        wb.save(xlsx_file_path)
        result['xlsx_file_url'] = f"{settings.MEDIA_URL}reports/{xlsx_file_path.name}"

    return result