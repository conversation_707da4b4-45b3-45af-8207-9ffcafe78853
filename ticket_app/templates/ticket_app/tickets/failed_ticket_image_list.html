{% extends "ticket_app/base/base.html" %}
{% load static %}
{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-md-10">
            <h2 class="text-center my-4">Failed Ticket Images</h2>
            <div class="table-responsive" style="max-height: 800px; overflow-y: auto;">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th scope="col">Image</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for image in failed_images %}
                        <tr>
                            <td class="text-center">
                                <img src="{{ image.images.url }}" alt="Ticket Image" width="500" class="img-fluid">
                            </td>
                            <td class="text-center">
                                <a href="{% url 'manual_create_ticket' image.pk %}" class="btn btn-primary btn-sm">Create Ticket Manually</a>
                                <button type="button" class="delete-failed-image bs-modal btn btn-sm btn-danger ms-2"
                                        data-form-url="{% url 'delete_failed_ticket_image' image.id %}">Delete</button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% include 'ticket_app/base/right_menu.html' %}
{% endblock %}

{% block scripts %}
    <script src="https://unpkg.com/intro.js/minified/intro.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.12.9/dist/umd/popper.min.js"
            integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
            crossorigin="anonymous"></script>
    <script src="{% static 'js/jquery-3.2.1.in.js' %}"></script>
    <script src="{% static 'js/jquery.bootstrap.modal.forms.js' %}"></script>
    <script src="{% static 'frontend/index.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <script type="text/javascript">
        // Delete failed image buttons - formURL is retrieved from the data of the element
        $(".delete-failed-image").each(function () {
            $(this).modalForm({formURL: $(this).data("form-url"), isDeleteForm: true});
        });
    </script>
{% endblock %}
