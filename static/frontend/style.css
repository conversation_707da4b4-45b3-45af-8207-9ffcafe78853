@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

:root {
    /* Modern Color Palette */
    --color-primary: #3b82f6;
    --color-primary-dark: #2563eb;
    --color-primary-light: #93c5fd;
    --color-secondary: #6366f1;
    --color-accent: #8b5cf6;

    --color-danger: #ef4444;
    --color-danger-light: #fecaca;
    --color-success: #10b981;
    --color-success-light: #d1fae5;
    --color-warning: #f59e0b;
    --color-warning-light: #fef3c7;
    --color-info: #06b6d4;
    --color-info-light: #cffafe;

    --color-white: #ffffff;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    --color-dark: var(--color-gray-800);
    --color-dark-variant: var(--color-gray-600);
    --color-light: var(--color-gray-100);
    --color-background: var(--color-gray-50);

    /* Modern Spacing & Sizing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;

    --card-border-radius: var(--border-radius-xl);
    --border-radius-1: var(--border-radius-sm);
    --border-radius-2: var(--border-radius-md);
    --border-radius-3: var(--border-radius-lg);

    --card-padding: var(--spacing-xl);
    --padding: var(--spacing-lg);

    /* Modern Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --box-shadow: var(--shadow-lg);

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-index scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}



/* Dark theme table styles */
.dark-theme-variables .table-responsive table th,
.dark-theme-variables .table-responsive table td {
    color: var(--color-white);  /* Text color for dark theme */
}


/* Dark theme table header and row background */
.dark-theme-variables .table-responsive table thead th {
    background-color: var(--color-dark-variant);  /* Dark theme header background */
}

.dark-theme-variables .table-responsive table tbody tr {
    background-color: var(--color-dark);  /* Dark theme row background */
}

.dark-theme-variables .table-responsive table tbody tr:hover {
    background-color: var(--color-primary-variant);  /* Row hover effect in dark theme */
}


 .manual-ticket-wrapper {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa; /* Light grey background to highlight the form */
    padding: 40px 20px;
  }

  .ticket-image-container img {
    max-width: 100%;
    height: auto;
    border: 1px solid #dee2e6;
    padding: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }

  .ticket-form-container {
    max-width: 100%;
  }

  .card {
    border-radius: 10px;
  }

  @media (max-width: 768px) {
    .manual-ticket-wrapper {
      padding: 20px;
    }

    .row {
      flex-direction: column;
    }

    .ticket-image-container, .ticket-form-container {
      max-width: 100%;
    }
  }


  .ticket-image-container {
    margin-bottom: 20px;
  }

  .ticket-image-container img {
    max-width: 100%;
    height: auto;
    border: 1px solid #ccc;
    padding: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }


.recently-edited-tickets-scrollable {
    max-height: 600px; /* Adjust the height to fit your needs */
    overflow-y: auto;
    border: 1px solid #28292a; /* Optional: adds a border around the scrollable area */
    padding: 10px; /* Optional: adds padding for better spacing */
}

.dark-theme-variables {
    --color-background: #0f172a;
    --color-white: #1e293b;
    --color-gray-50: #1e293b;
    --color-gray-100: #334155;
    --color-gray-200: #475569;
    --color-gray-300: #64748b;
    --color-gray-400: #94a3b8;
    --color-gray-500: #cbd5e1;
    --color-gray-600: #e2e8f0;
    --color-gray-700: #f1f5f9;
    --color-gray-800: #f8fafc;
    --color-gray-900: #ffffff;

    --color-dark: var(--color-gray-100);
    --color-dark-variant: var(--color-gray-400);
    --color-light: var(--color-gray-800);

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);
    --box-shadow: var(--shadow-lg);
}

.date {
    display: block;
    /*background: var(--color-background);*/
    /*border-radius: var(--border-radius-1);*/
    margin-top: 1rem;
    padding: 0.5rem 1.6rem;
}


/* Modern CSS Reset */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: 1.6;
    color: var(--color-dark);
    background-color: var(--color-background);
    overflow-x: hidden;
    min-height: 100vh;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Remove default styling */
ul, ol {
    list-style: none;
}

a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

button {
    font-family: inherit;
    font-size: inherit;
    border: none;
    background: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    outline: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}


/*.center {*/
/*  position: absolute;*/
/*  left: 50%;*/
/*  top: 50%;*/
/*  transform: translate(-50%, -50%);*/
/*}*/

/*:modal {*/
/*    border: 5px dashed var(--color-primary);*/
/*    display: flex;*/
/*    align-items: center;*/
/*    background-color: var(--color-background);*/
/*    color: var(--color-dark);*/
/*    box-shadow: var(--box-shadow);*/
/*    position: absolute;*/
/*    float: left;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

#favDialog{
    border: 5px dashed var(--color-primary);
    display: flex;
    align-items: center;
    background-color: var(--color-background);
    color: var(--color-dark);
    box-shadow: var(--box-shadow);
    position: absolute;
    /*float: left;*/
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}



/*/* The Modal (background) */
/*.modal {*/
/*    border: 5px dashed var(--color-primary);*/
/*    display: flex;*/
/*    align-items: center;*/
/*    background-color: var(--color-background);*/
/*    color: var(--color-dark);*/
/*    box-shadow: var(--box-shadow);*/
/*    position: absolute;*/
/*    !*float: left;*!*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    transform: translate(-50%, -50%);*/
/*}*/

/* Modal Content */
.modal-content {
  position: relative;
  background-color: var(--color-background);
    color: var(--color-dark);
  margin: 1rem auto;
  padding: 0;
  max-width: 95%;
  /*box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);*/
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}


.modal.draggable .modal-header {
    cursor: move;
}



/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-md);
    color: var(--color-dark);
}

h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    letter-spacing: -0.025em;
}

h2 {
    font-size: var(--font-size-3xl);
    font-weight: 600;
    letter-spacing: -0.025em;
}

h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
}

h4 {
    font-size: var(--font-size-xl);
    font-weight: 600;
}

h5 {
    font-size: var(--font-size-lg);
    font-weight: 500;
}

h6 {
    font-size: var(--font-size-base);
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--color-dark-variant);
    line-height: 1.7;
}

small {
    font-size: var(--font-size-sm);
    color: var(--color-gray-500);
}

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/*.profile-photo {*/
/*    width: 2.8rem;*/
/*    height: 2.8rem;*/
/*    border-radius: 50%;*/
/*    overflow: hidden;*/
/*}*/

/* Utility Classes */
.text-muted { color: var(--color-gray-500); }
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-success { color: var(--color-success); }
.text-danger { color: var(--color-danger); }
.text-warning { color: var(--color-warning); }
.text-info { color: var(--color-info); }
.text-white { color: var(--color-white); }
.text-dark { color: var(--color-dark); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-success { background-color: var(--color-success); }
.bg-danger { background-color: var(--color-danger); }
.bg-warning { background-color: var(--color-warning); }
.bg-info { background-color: var(--color-info); }
.bg-white { background-color: var(--color-white); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }

/* Legacy support */
.primary { color: var(--color-primary); }
.danger { color: var(--color-danger); }
.success { color: var(--color-success); }
.warning { color: var(--color-warning); }
.logo-text { color: var(--color-primary); }

b, strong {
    color: var(--color-dark);
    font-weight: 600;
}

/* Spacing utilities */
.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Modern Sidebar Navigation */
aside {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: var(--color-white);
    border-right: 1px solid var(--color-gray-200);
    z-index: var(--z-fixed);
    transition: transform var(--transition-normal);
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

aside .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-white);
    position: sticky;
    top: 0;
    z-index: 10;
}

aside .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

aside .logo img {
    width: 8rem;
    height: 2rem;
    object-fit: contain;
}

aside .close {
    display: none;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    background: var(--color-gray-100);
    color: var(--color-gray-600);
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

aside .close:hover {
    background: var(--color-gray-200);
    color: var(--color-gray-800);
}

aside .sidebar {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-xs);
}

aside .sidebar a {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    color: var(--color-gray-600);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    min-height: 48px;
}

aside .sidebar a span {
    font-size: 1.25rem;
    transition: all var(--transition-fast);
}

aside .sidebar a h3 {
    font-size: var(--font-size-base);
    font-weight: 500;
    margin: 0;
}

aside .sidebar a:hover {
    background: var(--color-primary-light);
    color: var(--color-primary-dark);
    transform: translateX(4px);
}

aside .sidebar a.active {
    background: var(--color-primary);
    color: var(--color-white);
    box-shadow: var(--shadow-md);
}

aside .sidebar a.active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--color-white);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

aside .sidebar a.active span,
aside .sidebar a.active h3 {
    color: var(--color-white);
}

aside .sidebar a:last-child {
    margin-top: auto;
    border-top: 1px solid var(--color-gray-200);
    padding-top: var(--spacing-lg);
}

/* Modern Main Content Area */
main {
    margin-left: 280px;
    min-height: 100vh;
    padding: var(--spacing-xl);
    background: var(--color-background);
    transition: margin-left var(--transition-normal);
}

main .date {
    display: inline-block;
    background: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

main .date .filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

main .date input[type="date"] {
    background: transparent;
    color: var(--color-dark);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: border-color var(--transition-fast);
}

main .date input[type="date"]:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px var(--color-primary-light);
}

.insights {
    display: grid;
    grid-template-columns: repeat(3, 2fr);
    gap: 1.6rem;
}

.insights img{
    width: 500px;
    height: 500px;
}

.insights > div {
    background: var(--color-white);
    padding: var(--card-padding);
    border-radius: var(--card-border-radius);
    margin-top: 1rem;
    box-shadow: var(--box-shadow);
    transition: all 300ms ease;
}

.insights > div:hover {
    box-shadow: none;
}

.insights > div span {
    background: var(--color-primary);
    padding: 0.5rem;
    border-radius: 50%;
    color: var(--color-white);
    font-size: 2rem;
}

.insights > div.tickets span{
    background: var(--color-danger);
}

.insights > div.income span{
    background: var(--color-success);
}


.insights > div .middle{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.insights h3{
    margin: 1rem 0 0.6rem;
    font-size: 1rem;
}

.insights .progress {
    position: relative;
    width: 92px;
    height: 92px;
    border-radius: 50%;
}

.insights svg{
    width: 7rem;
    height: 7rem;
}

.insights svg circle {
    fill: none;
    stroke: var(--color-primary);
    stroke-width: 14;
    stroke-linecap: round;
    transform: translate(5px, 5px);
    stroke-dasharray: 110;
    stroke-dashoffset: 92;
}

.insights .yards svg circle{
    stroke-dashoffset: -30;
    stroke-dasharray: 200;
}

.insights .tickets svg circle{
    stroke-dashoffset: 20;
    stroke-dasharray: 80;
}

.insights .income svg circle{
    stroke-dashoffset: 35;
    stroke-dasharray: 110;
}

.insights .progress .number {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.insights small {
    margin-top:  1.6rem;
    display: block;
}

.recent-tickets{
    margin-top: 2rem;
    height: 850px;
}

.recent-tickets h2{
    margin-top: .8rem;
}

.ticket-table{
    height: 850px;
    overflow-y: scroll;
    overflow-x: scroll;

}

.recent-tickets table {
    background: var(--color-white);
    width: 96%;
    border-radius: var(--card-border-radius);
    padding: var(--card-padding);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: all 300ms ease;

}

.recent-tickets table img {
    background: var(--color-white);
    height: 700px;
    width: 500px;
    align-items: center;
    justify-content: center;
    border-radius: var(--card-border-radius);
    padding: var(--card-padding);
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: all 300ms ease;
}

.recent-tickets table:hover{
    box-shadow: none;
}

main table tbody td{
    height: 2.8rem;
    border-bottom: 1px solid var(--color-light);
    color: var(--color-dark-variant);
}

main table tbody tr:last-child td{
    border: none;

}

main table tbody th {
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    z-index: 2;
}

main .recent-tickets a{
    text-align: center;
    display: block;
    margin: 1rem auto;
    color: var(--color-primary);

}

.right{
    margin-top: 1.4rem;
}

.right .top{
    display: flex;
    justify-content: end;
    gap: 2rem;
}

.right .top button{
    display: none;
}

.right .theme-toggle{
    background: var(--color-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 1.6rem;
    width: 4.2rem;
    cursor: pointer;
    border-radius: var(--border-radius-1);
}

.right .theme-toggle span{
    font-size: 1.2rem;
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.right .theme-toggle span.active{
    background: var(--color-primary);
    color: white;
    border-radius: var(--border-radius-1);
}

.right .top .profile{
    display: flex;
    gap: 2rem;
    text-align: right;
}

.right .recent-update{
    margin-top: 1rem;
}

.right .recent-update h2 {
    margin-bottom: .8rem;
}

.right .recent-update .updates{
    background: var(--color-white);
    padding: var(--card-padding);
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    transition: all 300ms ease;
}

.right .recent-update .updates:hover {
    box-shadow: none;
}

.right .recent-update .updates .update{
    display: grid;
    grid-template-columns: 2.6rem auto;
    gap: 1rem;
    margin-bottom: 1rem;
}


.right .analytics {
    margin-top: 2rem;
}

.right .analytics h2{
    margin-bottom: 0.8rem;
}

.right .analytics .item {
    background: var(--color-white);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.7rem;
    padding: 1.4rem var(--card-padding);
    border-radius: var(--border-radius-3);
    box-shadow: var(--box-shadow);
    transition: all 300ms ease;
}

.right .analytics .item:hover{
    box-shadow: none;
}

.right .analytics .item .right {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin: 0;
    width: 100%;
}

.right .analytics .item .icon {
    padding: 0.6rem;
    color: var(--color-white);
    border-radius: 50%;
    background: var(--color-primary);
    display: flex;
}


.right .analytics .add-ticket {
    background: transparent;
    border: 2px dashed var(--color-primary);
    color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.right .analytics .add-ticket:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.right .analytics .add-ticket div{
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

.right .analytics .add-ticket div h3{
    font-weight: 600;
}

.questionable {
    background-color: rgba(217, 137, 137, 0.45);
}

.assumed_good{
    background-color: rgba(0, 147, 0, 0.45);
}

/* Modern Layout Container */
.container-fluid {
    display: flex;
    min-height: 100vh;
    background: var(--color-background);
}

/* Content wrapper for main + right sidebar */
.content-wrapper {
    flex: 1;
    display: flex;
    margin-left: 280px;
    transition: margin-left var(--transition-normal);
}

.main-content {
    flex: 1;
    padding: var(--spacing-xl);
}

.right-sidebar {
    width: 320px;
    background: var(--color-white);
    border-left: 1px solid var(--color-gray-200);
    padding: var(--spacing-xl);
    overflow-y: auto;
}

footer {
    height: 50vh;
    background: var(--color-dark);
    color: var(--color-dark);
}


/* ============= MODERN RESPONSIVE DESIGN ======================= */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    aside {
        width: 240px;
    }

    main {
        margin-left: 240px;
    }

    .content-wrapper {
        margin-left: 240px;
    }

    .right-sidebar {
        width: 280px;
    }
}

/* Tablet screens (768px to 1023px) */
@media (max-width: 1023px) {
    aside {
        width: 200px;
    }

    aside .sidebar a h3 {
        font-size: var(--font-size-sm);
    }

    main {
        margin-left: 200px;
        padding: var(--spacing-lg);
    }

    .content-wrapper {
        margin-left: 200px;
    }

    .right-sidebar {
        width: 260px;
        padding: var(--spacing-lg);
    }

    .insights {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    main .recent-tickets table {
        font-size: var(--font-size-sm);
    }
}


/* ============= MOBILE RESPONSIVE DESIGN ======================= */
@media (max-width: 768px) {
    /* Mobile-first layout */
    .container-fluid {
        flex-direction: column;
    }

    .content-wrapper {
        margin-left: 0;
        flex-direction: column;
    }

    /* Mobile sidebar */
    aside {
        position: fixed;
        left: -100%;
        top: 0;
        width: 280px;
        height: 100vh;
        background: var(--color-white);
        z-index: var(--z-modal);
        box-shadow: var(--shadow-2xl);
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    aside.mobile-open {
        transform: translateX(0);
    }

    aside .close {
        display: flex;
    }

    aside .sidebar a h3 {
        display: block;
        font-size: var(--font-size-base);
    }

    /* Mobile main content */
    main {
        margin-left: 0;
        padding: var(--spacing-md);
        padding-top: 80px; /* Account for mobile header */
    }

    /* Mobile header */
    .mobile-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 64px;
        background: var(--color-white);
        border-bottom: 1px solid var(--color-gray-200);
        z-index: var(--z-fixed);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 var(--spacing-lg);
        box-shadow: var(--shadow-sm);
    }

    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius-md);
        background: var(--color-gray-100);
        color: var(--color-gray-700);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .mobile-menu-btn:hover {
        background: var(--color-gray-200);
    }

    /* Hide right sidebar on mobile */
    .right-sidebar {
        display: none;
    }

    /* Mobile-specific right panel */
    .right {
        width: 100%;
        margin: var(--spacing-lg) 0;
        padding: 0 var(--spacing-md);
    }

    .right .top {
        position: relative;
        background: transparent;
        box-shadow: none;
        padding: 0;
        height: auto;
        margin-bottom: var(--spacing-lg);
    }

    .right .top button {
        display: none;
    }

    /* Mobile insights grid */
    .insights {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    /* Mobile tables */
    main .recent-tickets {
        margin-top: var(--spacing-lg);
        width: 100%;
    }

    main .recent-tickets table {
        width: 100%;
        font-size: var(--font-size-sm);
        overflow-x: auto;
        display: block;
        white-space: nowrap;
    }

    main .recent-tickets table thead,
    main .recent-tickets table tbody,
    main .recent-tickets table tr {
        display: block;
    }

    main .recent-tickets table td,
    main .recent-tickets table th {
        display: inline-block;
        vertical-align: top;
        padding: var(--spacing-sm);
        min-width: 120px;
    }

    /* Mobile forms */
    .date .filters {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    /* Mobile backdrop for sidebar */
    .mobile-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: var(--z-modal-backdrop);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .mobile-backdrop.active {
        opacity: 1;
        visibility: visible;
    }
}

/* ============= MODERN COMPONENT STYLES ======================= */

/* Modern Cards */
.card {
    background: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-gray-50);
}

.card-body {
    padding: var(--spacing-xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--color-gray-200);
    background: var(--color-gray-50);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 40px;
}

.btn-primary {
    background: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
}

.btn-primary:hover {
    background: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--color-gray-100);
    color: var(--color-gray-700);
    border-color: var(--color-gray-300);
}

.btn-secondary:hover {
    background: var(--color-gray-200);
    color: var(--color-gray-800);
}

.btn-danger {
    background: var(--color-danger);
    color: var(--color-white);
    border-color: var(--color-danger);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.btn-success {
    background: var(--color-success);
    color: var(--color-white);
    border-color: var(--color-success);
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
}

/* Modern Form Controls */
.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-dark);
    background: var(--color-white);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--border-radius-md);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-dark);
}

/* Modern Modal Styles */
.modal-content {
    background: var(--color-white);
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-gray-50);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--color-gray-200);
    background: var(--color-gray-50);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Modern Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th,
.table td {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid var(--color-gray-200);
}

.table th {
    background: var(--color-gray-50);
    font-weight: 600;
    color: var(--color-dark);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background: var(--color-gray-50);
}

/* Modern Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--border-radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--color-primary-light);
    color: var(--color-primary-dark);
}

.badge-danger {
    background: var(--color-danger-light);
    color: #dc2626;
}

.badge-success {
    background: var(--color-success-light);
    color: #059669;
}

.badge-warning {
    background: var(--color-warning-light);
    color: #d97706;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form enhancements */
.form-control.has-value {
    border-color: var(--color-success);
}

.form-control.is-invalid {
    border-color: var(--color-danger);
    box-shadow: 0 0 0 3px var(--color-danger-light);
}

/* Smooth transitions for all interactive elements */
* {
    transition: color var(--transition-fast),
                background-color var(--transition-fast),
                border-color var(--transition-fast),
                box-shadow var(--transition-fast),
                transform var(--transition-fast);
}

/* Focus styles for accessibility */
button:focus-visible,
.btn:focus-visible,
.form-control:focus-visible,
a:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    aside,
    .mobile-header,
    .right-sidebar,
    .btn,
    .mobile-backdrop {
        display: none !important;
    }

    main {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .container-fluid {
        max-width: none !important;
    }
}