// Modern Mobile Navigation
const sideMenu = document.querySelector("aside");
const menuBtn = document.querySelector("#menu-btn");
const mobileMenuBtn = document.querySelector("#mobile-menu-btn");
const closeBtn = document.querySelector("#close-btn");
const mobileBackdrop = document.querySelector("#mobile-backdrop");
const themeToggle = document.querySelector(".theme-toggle");

// Function to open mobile menu
function openMobileMenu() {
    if (sideMenu) {
        sideMenu.classList.add('mobile-open');
        if (mobileBackdrop) {
            mobileBackdrop.classList.add('active');
        }
        document.body.style.overflow = 'hidden';
    }
}

// Function to close mobile menu
function closeMobileMenu() {
    if (sideMenu) {
        sideMenu.classList.remove('mobile-open');
        if (mobileBackdrop) {
            mobileBackdrop.classList.remove('active');
        }
        document.body.style.overflow = '';
    }
}

// Desktop menu button (if exists)
if (menuBtn) {
    menuBtn.addEventListener('click', openMobileMenu);
}

// Mobile menu button
if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener('click', openMobileMenu);
}

// Close button
if (closeBtn) {
    closeBtn.addEventListener('click', closeMobileMenu);
}

// Mobile backdrop click
if (mobileBackdrop) {
    mobileBackdrop.addEventListener('click', closeMobileMenu);
}

// Close menu on escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeMobileMenu();
    }
});

// Handle window resize
window.addEventListener('resize', () => {
    if (window.innerWidth >= 768) {
        closeMobileMenu();
    }
});

// Modern Theme Toggle
if (themeToggle) {
    themeToggle.addEventListener('click', () => {
        document.body.classList.toggle('dark-theme-variables');

        const lightIcon = themeToggle.querySelector('span:nth-child(1)');
        const darkIcon = themeToggle.querySelector('span:nth-child(2)');

        if (lightIcon && darkIcon) {
            lightIcon.classList.toggle('active');
            darkIcon.classList.toggle('active');
        }

        // Save theme preference
        const isDark = document.body.classList.contains('dark-theme-variables');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });

    // Load saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme-variables');
        const lightIcon = themeToggle.querySelector('span:nth-child(1)');
        const darkIcon = themeToggle.querySelector('span:nth-child(2)');

        if (lightIcon && darkIcon) {
            lightIcon.classList.remove('active');
            darkIcon.classList.add('active');
        }
    }
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading states to buttons
document.querySelectorAll('button[type="submit"]').forEach(button => {
    button.addEventListener('click', function() {
        if (this.form && this.form.checkValidity()) {
            this.classList.add('loading');
            this.disabled = true;

            // Re-enable after 3 seconds as fallback
            setTimeout(() => {
                this.classList.remove('loading');
                this.disabled = false;
            }, 3000);
        }
    });
});

// Enhanced form validation feedback
document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('blur', function() {
        if (this.value.trim() !== '') {
            this.classList.add('has-value');
        } else {
            this.classList.remove('has-value');
        }
    });

    input.addEventListener('focus', function() {
        this.classList.remove('is-invalid');
    });
});

